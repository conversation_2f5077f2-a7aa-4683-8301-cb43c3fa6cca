<template>
	<view class="input-box">
		<view class="input" v-if="type === 'mobile'">
			<input
				type="numeric"
				maxlength="11"
				:placeholder-style="placeholderColor"
				:value="modelValue"
				@input="$emit('update:modelValue', $event.detail.value)"
				placeholder="输入绑定手机号" />
		</view>
		<view class="input" v-if="type === 'account'">
			<image
				class="icon"
				src="@/static/image/icon_accountinput.png"
				mode="widthFix"></image>
			<input
				type="text"
				:value="modelValue"
				:placeholder-style="placeholderColor"
				@input="$emit('update:modelValue', $event.detail.value)"
				placeholder="输入账号" />
		</view>
		<view class="input" v-if="type === 'password'">
			<input
				type="password"
				:value="modelValue"
				:placeholder-style="placeholderColor"
				@input="$emit('update:modelValue', $event.detail.value)"
				:placeholder="placeholder || '输入密码'" />
		</view>
		<template v-if="type === 'verificationCode'">
			<view class="input">
				<input
					type="number"
					:value="modelValue"
					:placeholder-style="placeholderColor"
					@input="$emit('update:modelValue', $event.detail.value)" />
				<view v-show="!isVerCount && disable" class="text-color-grey">{{
					verText
				}}</view>
				<view
					v-show="!isVerCount && !disable"
					class="text-color-assist"
					@click="getVerCode"
					>{{ verText }}</view
				>
				<view v-show="isVerCount" class="text-color-assist"
					>倒计时 {{ verCount }} s</view
				>
			</view>
			<view class="error-tip">
				如未绑定手机请联系客服电话：(+86）775-88328999
			</view>
		</template>

		<view class="error-tip">
			{{ errorText }}
		</view>
	</view>
</template>

<script>
import { RegMobile } from "@/utils/regEx.js"
import request from "@/utils/request.js"
export default {
	data() {
		return {
			isVerCount: false, //是否开启验证倒计时
			verCount: 0,
			verText: "获取验证码",
			errorText: ""
		}
	},
	props: {
		type: {
			type: String,
			default: "mobile"
		},
		disable: {
			type: Boolean,
			default: true
		},
		modelValue: String,
		placeholder: String,
		mobile: String
	},
	watch: {
		modelValue(newVal, oldVal) {
			this.errorText = ""
			switch (this.type) {
				case "mobile":
					const isMobile = RegMobile.test(newVal)
					this.$emit("verify", isMobile)
					if (!isMobile) {
						this.errorText = "手机号格式不正确"
					}
					break
			}
		}
	},
	computed: {
		placeholderColor() {
			return { color: "#9397A8" }
		}
	},
	methods: {
		getVerCode() {
			const params = {
				credential: this.mobile,
				type: 2 // 1 邮箱 2 手机号
			}
			uni.showLoading({
				title: "发送验证码"
			})
			request.casPost("/otp", params).then(res => {
				uni.hideLoading()
				uni.showToast({
					title: "验证码发送成功"
				})
				this.isVerCount = true
				this.verCount = 60
				const timer = setInterval(() => {
					this.verCount--
					if (this.verCount <= 0) {
						this.isVerCount = false
						this.verText = "重新获取"
						clearInterval(timer)
					}
				}, 1000)
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.input-box {
	position: relative;
	padding: 0rpx 60rpx 50rpx 60rpx;
	.input {
		background-color: #f5f5f5;
		display: flex;
		align-items: center;
		height: 96rpx;
		// border-radius: 25rpx;
		padding-left: 30rpx;
		padding-right: 30rpx;
		.icon {
			width: 42rpx;
			margin-right: 20rpx;
		}
		input {
			flex: 1;
		}
	}
	.error-tip {
		position: absolute;
		font-size: 24rpx;
		line-height: 50rpx;
		color: #d86666;
	}
}
</style>
