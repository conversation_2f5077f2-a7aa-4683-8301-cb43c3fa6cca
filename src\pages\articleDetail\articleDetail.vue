<template>
	<view v-if="!loading">
		<swiper class="swiper" :interval="3000" :duration="500" circular autoplay :indicator-dots="false">
			<block v-for="item in articleInfo?.publicizeImgUrl.split(',')">
				<swiper-item class="swiper-item">
					<image class="img" :src="host + item || '-'" mode="aspectFill" />
				</swiper-item>
			</block>
		</swiper>
		<view class="main">
			<view class="article">
				<view class="title">{{ articleInfo?.articleName }}</view>
				<view class="sub">
					<view class="time">{{
						dayjs(articleInfo?.publishTime).format("YYYY-MM-DD")
					}}</view>
					<image class="eye" src="../../static/image/message/eye.svg" mode="scaleToFill" />
					<view class="count">{{
						articleInfo && articleInfo?.readCount >= 100
							? (articleInfo?.readCount / 1000).toFixed(1) + "k"
							: articleInfo?.readCount
					}}</view>
				</view>

				<view style="display: flex; align-items: center">
					<image class="user" src="../../static/image/message/user.svg" mode="scaleToFill" />

					<view class="author">{{ articleInfo?.articleAuthor }}</view>
				</view>
			</view>

			<view class="content" v-html="markdownToHtml(articleInfo?.articleContent)"></view>
		</view>
	</view>
</template>
<script setup lang="ts">
import request from "@/utils/request.js"
import { getRoute, markdownToHtml } from "@/utils/tool.js"
import dayjs from "dayjs"
import { onMounted, ref } from "vue"
import { getEnv } from "@/utils/getEnv";

interface ArticleInfo {
	articleAuthor: string
	articleContent: string
	articleName: string
	articleSort: number
	articleSource: number
	articleType: number
	enableState: number
	id: string
	publicizeImgUrl: string
	publishTime: string
	readCount: number
	recommendState: number
	storeId: string
}

const host = getEnv().VITE_IMG_HOST
const { storeId, id } = getRoute.params()
const articleInfo = ref<ArticleInfo>()

const loading = ref(true)

const fetchArticleInfo = async () => {
	try {
		uni.showLoading({
			title: "加载中",
			mask: true
		})
		const { data } = await request.get(`/article/h5/info`, { id })
		articleInfo.value = data
		loading.value = false
		uni.hideLoading()
	} catch (error) {
		console.log(error)
	}
}

onMounted(() => {
	fetchArticleInfo()
})
</script>
<style lang="scss" scoped>
.swiper {
	height: 422rpx;
	// background-color: orange;
	overflow: hidden;
	position: relative;

	.swiper-item {
		display: block;
		text-align: center;
		// background-color: orange;
	}

	.img {
		width: 100%;
		// height: 100%;
	}

	:global(.uni-swiper-dot) {
		width: 8rpx;
		height: 8rpx;
		margin-right: 6rpx !important;
		background-color: #d8d8d8;
	}

	:global(.uni-swiper-dot-active) {
		transition: 0.3s;
		width: 16rpx;
		height: 8rpx;
		border-radius: 4rpx;
		background-color: #ffffff;
	}
}

.main {
	padding: 20rpx 30rpx;

	.article {
		width: 100%;

		.title {
			font-size: 36rpx;
			font-weight: 500;
			color: #14131f;
			line-height: 50rpx;
		}

		.sub {
			display: flex;
			margin: 10rpx 0 16rpx 0;
			color: rgba(20, 19, 31, 0.5);
			height: 32rpx;
			font-size: 24rpx;
			font-weight: 400;
			line-height: 32rpx;

			.eye {
				width: 32rpx;
				height: 32rpx;
				margin-left: 45rpx;
				margin-right: 2rpx;
			}
		}

		.user {
			height: 36rpx;
			width: 36rpx;
			margin-right: 8rpx;
		}

		.author {
			height: 32rpx;
			font-size: 23rpx;
			font-weight: 400;
			color: #14131f;
			line-height: 32rpx;
		}
	}

	.content {
		margin-top: 40rpx;
	}
}
</style>
