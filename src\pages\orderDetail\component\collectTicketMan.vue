<template>
	<view class="collect-ticket-man" :class="[props.theme]">
		<view
			class="title"
			v-if="modelValue.name || modelValue.identity || modelValue.mobile">
			联系人
		</view>
		<view class="item" v-if="modelValue.name">
			<view class="key"> 姓&emsp;名 </view>
			{{ modelValue.name }}
		</view>
		<view class="item" v-if="modelValue.mobile">
			<view class="key"> 手机号 </view>
			<view class="">
				{{
					`${modelValue.mobile.slice(0, 3)}****${modelValue.mobile.slice(-4)}`
				}}
			</view>
		</view>
		<view class="item" v-if="modelValue.identity">
			<view class="key"> 身份证 </view>
			{{
				`${modelValue.identity.slice(
					0,
					3
				)}***********${modelValue.identity.slice(-4)}`
			}}
		</view>
	</view>
</template>
<script setup>
import { toRefs, reactive, ref } from "vue"
const props = defineProps({
	modelValue: {
		type: Array,
		default: []
	},
	//主题
	theme: {
		type: String,
		default: "greenTheme"
	}
})
</script>
<style lang="scss" scoped>
.collect-ticket-man.greenTheme {
	--color1: #d43e3e;
	--color2: #ffb4b4;
	--bg1: #fff8f8;
	--border-btm: 1px solid rgba(255, 152, 152, 0.44);
}
.collect-ticket-man.blueTheme {
	--color1: #024abb;
	--color2: #61b4fc;
	--bg1: #f8fbff;
	--border-btm: 1px solid #c8e5ff;
}

.collect-ticket-man {
	margin: 40rpx 0;
	padding: 30rpx 30rpx 10rpx;
	background: var(--bg1);
	border-radius: 24rpx;
	> .title {
		margin-bottom: 30rpx;
		padding-bottom: 27rpx;
		font-size: 36rpx;
		font-weight: 500;
		color: #050505;
		line-height: 50rpx;
		border-bottom: var(--border-btm);
	}
	> .item {
		display: flex;
		align-items: center;
		margin: 24rpx 0;
		.key {
			margin-right: 40rpx;
		}
	}
}
</style>
