<template>
	<view class="ticket-package">
		<view class="title">
			<view>套票信息</view>
			<!-- <view @tap="deleteItem()">
				<image
					class="delete-icon"
					src="@/static/image/trash-can-icon.png"
					mode=""></image>
			</view> -->
		</view>
		<view class="tourist-item" v-for="(item, index) in goodsList" :key="index">
			<view style="width: 100%">
				<view class="ticket-info">
					<view class="title">
						<text class="title-one">
							{{ item.title }}
						</text>
						<text class="title-tow"> （{{ item.goodsType }}） </text>
					</view>
				</view>
				<view class="ticket-info time">
					{{ timeShare }}
				</view>
				<!-- 分时时段 -->
				<view class="time-period" v-show="timeLaws?.[item.goodsId]?.[props.ticketInfo?._day]?.length">
				<view class="title">
					<y-font-weight>分时时段</y-font-weight>
				</view>
				<view>
					<view class="period-list">
					<view class="period-item-box"  v-for="(_times) in timeLaws?.[item.goodsId]?.[props.ticketInfo?._day]">
						<view
						class="period-item"
						:class="{
							disabled: _times?.disabled,
							active: _times?.active,
						}"
						@click="onTimePeriodClick(_times, item.goodsId)">
							{{_times?.timeShareBeginTime}} - {{ _times?.timeShareEndTime }}
						</view>
					</view>
					</view>
				</view>
				</view>
        		<!-- 实名制 -->
				<template v-if="item.isRealName">
					<Linkman
						ref="linkmanRef"
						@onUpdate="onUpdateLinkMan"
						:isOne="true"
						v-model:selectedContacts="item.realNameInfo"
						:max="1"></Linkman>
					<view
						class="tourist-item"
						style="margin: 30rpx 0"
						v-for="(e, i) in item.realNameInfo"
						:key="i">
						<image
							v-if="!isOwner"
							@click="delLinkMan(index)"
							class="minus"
							src="@/static/image/minus-circle.png"
							mode="scaleToFill" />
						<view>
							<view class="input-box">
								<view class="name"> 姓&emsp;名 </view>
								<view>{{ e.name }}</view>
							</view>
							<view class="input-box">
								<view class="name"> 身份证 </view>
								<view>{{ e.idCard }}</view>
							</view>
							<view
								v-if="!e.faceImageUrl && faceCheck(item).onlyFace"
								class="face-tip"
								>请上传人脸信息，否则影响开闸！</view
							>
						</view>
						<image
							v-if="!e.faceImageUrl && faceCheck(item).isFace"
							class="face-icon"
							@click.stop="
							Tool.goPage.push(
									'/pages/contactsList/contactsList?page=editPage&id=' + e.id
								)
							"
							src="@/static/image/order/orderdetal-face-icon.png"
							mode="widthFix" />
					</view>
				</template>
			</view>
		</view>
	</view>
</template>
<script setup>
import { toRefs, watch, reactive, ref, onMounted } from "vue"
import Linkman from "@/pages/book/component/linkman.vue"
import { ticketType, goodsType } from "@/utils/constant.js"
name: "ticket-package"
const props = defineProps({
	modelValue: {
		type: Array,
		default: []
	},
	ticketNum: {
		type: Number,
		default: 1
	},
	ticketInfo: {
		type: Object,
		default: {}
	},
	goodsList: {
		type: Array,
		default: []
	}
})
const emits = defineEmits(["onChangeComposeTimePeriod"])
const linkManList = ref([])
const ticketList = reactive([])
const linkmanRef = ref(null)
// 更新联系人
const onUpdateLinkMan = () => {
	if (linkmanRef.value && linkmanRef.value.length > 0) {
		linkmanRef.value.forEach((e, i) => {
			e.init()
		})
	}
}

const faceCheck = item => {
	let { checkType } = item
	checkType = [1, 2]
	console.log(checkType)
	const isFace = checkType.includes(1)
	const onlyFace = isFace && checkType.length === 1
	return { isFace, onlyFace }
}


// 分时时段
let timeLaws = reactive({})

// 时段改变
const onTimePeriodClick = (item, goodsId) => {
  if (item.disabled) return;
  timeLaws?.[goodsId][props.ticketInfo._day].forEach((e) => {
    if (e.timeShareId === item.timeShareId) {
      e.active = true;
    } else {
      e.active = false;
    }
  })
  emits('onChangeComposeTimePeriod', timeLaws);
}


watch(
  () => props.ticketInfo._day,
  () => {
    // 初始化默认时间和默认时间段数据，对数据进行排序
    (props.goodsList || []).forEach((goodsItem) => {
      timeLaws[goodsItem?.goodsId] = {};
      if (goodsItem?.timeLaws?.length) {
        [...goodsItem?.timeLaws].forEach((dayItem) => {
          // 对时间段进行排序
          dayItem?.timeShareDateList?.sort((a, b) => {
            if (compareTimes(a.timeShareBeginTime, b.timeShareBeginTime) === -1) return -1;
            return 1;
          });
          // 处理置灰逻辑
          let _flag = false; // 是否赋值过
          const _list = (dayItem?.timeShareDateList || []).map((_item) => {
            // 禁用：(timeShareDate在当天之前或等于当天 && timeShareEndTime在当前时间段之前) || 没有库存
            const _disabled = (!dayjs(dayItem?.timeShareData).isAfter(dayjs()) && _item?.timeShareEndTime && compareTimes(_item?.timeShareEndTime, dayjs().format('HH:mm')) === -1) || !_item?.stockAmount;
            const _newItem = {
              ..._item,
              disabled: _disabled,
              active: !_disabled && !_flag, // 设置第一个可用的时段为默认值
            }
            if (!_disabled && !_flag) _flag = true;
            return _newItem;
          })
          timeLaws[goodsItem?.goodsId][dayItem.timeShareData] = _list
        });
      }
    });
    
    emits('onChangeComposeTimePeriod', timeLaws);
  },
  {
		immediate: true,
	}
)


onMounted(() => {
	// props.goodsList.forEach(element => {
	// 	for (let i = 0; i < element.num; i++) {
	// 		ticketList.push({
	// 			title: `${element.proName}-${ticketType[element.proType]}`,
	// 			goodsType: goodsType[element.goodsType],
	// 			isRealName: element.isRealName == 1,
	// 			timeShare:
	// 				element.timeShareId == 0
	// 					? `${element.validityDay}天有效`
	// 					: `${element.timeShareBeginTime}-${element.timeShareEndTime}`,
	// 			realNameInfo: []
	// 		})
	// 	}
	// })
})
// watch(
// 	() => props.modelValue,
// 	value => {
// 		value.map(n => {
// 			n.map(e => {
// 				console.log(e.goodsDetail.buyOwner)
// 				if (e.identity == "" && !e.realNameInfo) {
// 					e.realNameInfo = []
// 					for (let i = 0; i < e.goodsDetail.num; i++) {
// 						// 仅限本人购买回填用户信息
// 						const user = { identity: "", name: "", isLinkMan: false }
// 						if (!!e.goodsDetail.buyOwner) {
// 							user.identity = uni.getStorageSync("autonym").idNumber
// 							user.name = uni.getStorageSync("autonym").idName
// 						}
// 						e.realNameInfo.push(user)
// 					}
// 				}
// 			})
// 		})
// 	},
// 	{ deep: true }
// )
const delLinkMan = index => {
	props.goodsList[index].realNameInfo = []
}
// const add=()=>{
// 	let arr=JSON.parse(JSON.stringify(props.modelValue[0]));
// 	arr.map(n=>{
// 		if(n.realNameInfo){
// 			n.realNameInfo.map(e=>{
// 				e.identity='';
// 				e.name='';
// 			})
// 		}
// 	})
//   props.modelValue.push(arr)
// };
// const deleteItem = k => {
// 	props.modelValue.splice(k, 1)
// }
// const emits = defineEmits(["linkman"])
// const linkman = (k, index, i) => {
// 	emits("linkman", { open: true, place: [k, index, i] })
// }
// const onCheckMan = (k, index, i) => {
// 	let arr = JSON.parse(JSON.stringify(props.modelValue[k][index].realNameInfo))
// 	arr.forEach((e, o) => {
// 		if (i === o) {
// 			e.isLinkMan = !e.isLinkMan
// 		}
// 	})
// 	props.modelValue[k][index].realNameInfo = arr
// }
</script>
<style lang="scss" scoped>
.ticket-package {
	background-color: #fff;
	border-radius: 24rpx;
	margin-bottom: 40rpx;
	padding: 0 0 15rpx 0;

	> .title {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin: 0 30rpx;
		padding: 30rpx 0;
		font-size: 36rpx;
		font-weight: 600;
		color: #050505;
		line-height: 32rpx;
		// background: linear-gradient(321deg, #D8A4FF 0%, #00EADA 100%);
		border-radius: 24px 24px 0px 0px;
		border-bottom: 2rpx solid #e4e4e4;
		.delete-icon {
			width: 30rpx;
			height: 33rpx;
		}
	}
	.tourist-item {
		display: flex;
		margin: 25rpx 30rpx;
		background-color: #fff;
		overflow: hidden;
		> view {
			// margin: 24rpx 0;
		}
		.ticket-info {
			display: flex;
			align-items: center;
			justify-content: space-between;
			&.time {
				margin-top: 14rpx;
				font-size: 26rpx;
				font-weight: 400;
				color: #050505;
				line-height: 37rpx;
			}
			.title {
				flex: 1;
				display: flex;
				font-size: 32rpx;
				font-weight: 500;
				color: #050505;
				// line-height: 32rpx;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
				.title-one {
					font-weight: 500;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
				}
				.title-tow {
					flex: none;
					text-align: center;
					color: #30c3cf;
				}
				text {
					display: inline-block;
				}
			}
			.type {
				font-size: 32rpx;
				font-weight: 500;
				color: #30c3cf;
				line-height: 45rpx;
			}
			.linkman {
				display: block;
				margin-left: auto;
				font-size: 26rpx;
				font-weight: 400;
				color: #3c93ff;
				line-height: 37rpx;
			}
		}
		.input-box {
			display: flex;
			align-items: center;
			margin: 18rpx 0;
			font-size: 28rpx;
			font-weight: 400;
			color: #040404;
			line-height: 28rpx;
			.name {
				margin-right: 40rpx;
			}
			input {
				flex: 1;
			}
		}
		.set-linkman {
			display: flex;
			align-items: center;
			margin-bottom: 40rpx;
			font-size: 24rpx;
			font-weight: 400;
			color: #999999;
			.title {
				margin-right: 10rpx;
			}
		}
		.demarcation {
			margin: 0rpx 0 0rpx 0;
			border-top: 2rpx solid rgba(228, 228, 228, 0.39);
		}
	}

  .time-period {
    margin: 16rpx 30rpx 0 30rpx;
		padding-bottom: 25rpx;
		border-bottom: 2rpx solid rgba(228, 228, 228, 0.39);
    .title {
      font-size: 28rpx;
    }
    .period-list {
      display: flex;
      flex-wrap: wrap;
      padding: 16rpx 0 0 0;
      .period-item-box {
        display: flex;
        margin: 0 0 16rpx 0;
        width: 33.3%;
        font-size: 22rpx;
        .period-item {
          padding: 4rpx 8rpx;
          background: #fbfbfb;
          border: 1px solid #e1e1e2;
          border-radius: 0.25rem;
        }
        .active {
          background: rgba(var(--theme-bg), 0.1) !important;
          border: 1px solid var(--theme-color) !important;
        }
        .disabled {
          background-color: #f0f0f0;
        }
      }
    }
  }
}
.add-ticket {
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 20rpx;
	height: 96rpx;
	font-size: 36rpx;
	font-weight: 500;
	color: #ffffff;
	background: #7ed3da;
	border-radius: 25rpx;
}
.set-linkman {
	display: flex;
	align-items: center;
	margin-bottom: 40rpx;
	font-size: 24rpx;
	font-weight: 400;
	color: #999999;
	.title {
		margin-right: 10rpx;
	}
}
.tourist-item {
	display: flex;
	// flex-direction: column;
	align-items: center;
	position: relative;
	// padding: 0 0 0 70rpx;
	margin: 0 30rpx;
	background-color: #fff;
	overflow: hidden;
	&:last-child {
		border-radius: 0px 0px 24rpx 24rpx;
	}
	&:not(:last-child) {
		border-bottom: 2rpx solid rgb(228, 228, 228, 0.4);
	}
	.minus {
		width: 40rpx;
		height: 40rpx;
		margin-right: 30rpx;
	}

	.input-box {
		display: flex;
		align-items: center;
		margin: 30rpx 0;
		font-size: 28rpx;
		font-weight: 400;
		color: #040404;
		line-height: 28rpx;
		.name {
			margin-right: 40rpx;
		}
		.input {
			flex: 1;
		}
	}
	.linkman {
		margin-left: auto;
		font-size: 26rpx;
		font-weight: 400;
		color: #3c93ff;
		line-height: 37rpx;
	}
	.set-linkman {
		display: flex;
		align-items: center;
		margin-bottom: 40rpx;
		font-size: 24rpx;
		font-weight: 400;
		color: #999999;
		.title {
			margin-right: 10rpx;
		}
	}
	.face-tip {
		margin-top: 15rpx;
		font-size: 24rpx;
		font-weight: 400;
		color: red;
	}
	.face-icon {
		margin-left: auto;
		width: 80rpx;
		height: 80rpx;
	}
}
</style>
