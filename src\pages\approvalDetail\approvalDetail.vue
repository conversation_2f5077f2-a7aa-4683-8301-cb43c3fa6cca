<template>
	<y-nav-bar backBtn>审批详情</y-nav-bar>
	<view class="approval-detail">
		<view class="approval-status">
			<view class="title">{{
				routerParams.status === "reject" ? "审批驳回" : "审批中"
			}}</view>
			<view class="reason" v-if="routerParams.reason">驳回原因:{{ routerParams.reason }}</view>
		</view>
		<view class="approval-content">
			<view class="title">
				<y-font-weight>审核内容</y-font-weight>
			</view>
			<view class="content">{{ routerParams.notice }}</view>
			<view class="title">
				<y-font-weight>图片信息</y-font-weight>
			</view>
			<view class="imgList">
				<image v-for="(e, i) in imgList" style="margin-right: 10rpx" :src="imgViewHost + e"></image>
			</view>
		</view>
	</view>
	<y-button :disable="false" v-if="routerParams.status === 'reject'" @tap="
		Tool.goPage.replace(
			`/pages/approval/approval?id=${routerParams.id}&issueId=${routerParams.issueId}&goodsId=${routerParams.goodsId}&type=${routerParams.type}&edit`
		)
		">编辑</y-button>
</template>

<script setup>
import { onLoad } from "@dcloudio/uni-app"
import { toRefs, reactive, ref, watch, onBeforeMount } from "vue"
import { getEnv } from "@/utils/getEnv";
const routerParams = reactive({})
const imgViewHost = ref(getEnv().VITE_IMG_HOST)
const imgList = ref([])
onLoad(option => {
	for (let key in option) {
		routerParams[key] = option[key]
	}
	console.log("option", option, routerParams)
	if (option.picture) {
		imgList.value = option.picture.split(",")
	}
})

// uni.$emit("handClickXXX",{edit:'edit'});
</script>

<style lang="scss">
.approval-detail {
	.approval-status {
		background: linear-gradient(146deg, #31c7d3 0%, #b5ebef 100%, #b5ebef 100%);
		padding: 30rpx 40rpx;
		color: #ffffff;
		height: 164rpx;
		display: flex;
		flex-direction: column;
		justify-content: end;

		.title {
			font-size: 48rpx;
			font-weight: 600;
			color: #ffffff;
			line-height: 67rpx;
		}

		.reason {
			font-size: 26rpx;
			margin-top: 15rpx;
		}
	}

	.approval-content {
		padding: 30rpx 40rpx;
		background-color: #fff;

		>.title {
			margin-bottom: 15rpx;
			font-size: 34rpx;
			font-weight: 500;
			color: #000000;
			line-height: 48rpx;
		}

		>.content {
			margin-bottom: 50rpx;
			font-size: 28rpx;
			font-weight: 400;
			color: #050505;
			line-height: 40rpx;
		}

		image {
			width: 145rpx;
			height: 145rpx;
			border-radius: 16rpx;
		}

		.imgList {
			.img {
				width: 145rpx;
				height: 145rpx;
				border-radius: 16rpx;
			}
		}
	}
}
</style>
