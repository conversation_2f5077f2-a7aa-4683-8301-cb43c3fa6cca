<template>
	<view class="tab_bar">
		<view
			v-for="(value, index) in tabList"
			:key="index"
			:class="{
				active: index == tabList.findIndex(v => v.link == modelValue)
			}"
			@click="goTab(value.link, value.name)">
			<image
				v-if="value.icon.includes('http')"
				class="icon"
				:src="value.icon"
				mode="aspectFit" />
			<y-svg
				v-else
				class="icon"
				:name="`tab_icon/${value.icon}`"
				:style="`width: 60rpx; height: 60rpx; color: ${tabColor};`" />
			<image
				v-if="value.icon_active.includes('http')"
				class="icon--active"
				:src="value.icon_active"
				mode="aspectFit" />
			<y-svg
				v-else
				class="icon--active"
				:name="`tab_icon/${value.icon_active}`"
				:style="`width: 60rpx; height: 60rpx; color: ${tabColor};`" />
			<text>{{ value.name }}</text>
		</view>
	</view>
</template>

<script setup>
const props = defineProps(["modelValue"])
const emits = defineEmits(["update:modelValue"])
let tabList = ref([])
let tabColor = ref("")
onBeforeMount(async () => {
	const { shopNav, shopStyle } = await Tool.getThemeConfig()
	console.log("shopNav", shopNav)
	tabList.value = shopNav
	tabColor.value = shopStyle.color
})

const goTab = (key, title) => {
	emits("update:modelValue", key)
	uni.setNavigationBarTitle({ title })
	if (location.href.includes("curTab")) {
		const hash = location.href.split("?")
		const params = hash[1].split("&")
		params.map((item, index) => {
			if (item.includes("curTab")) {
				params[index] = `curTab=${key}`
			}
		})
		history.pushState(null, null, [hash[0], params.join("&")].join("?"))
	} else {
		history.pushState(null, null, `${location.href}&curTab=${key}`)
	}
}
</script>

<style lang="scss" scoped>
.tab_bar {
	width: 100%;
	height: 98rpx;
	border-top: 1rpx solid #e4e4e4;
	background-color: #fff;
	display: flex;
	position: fixed;
    bottom: 0;
	> view {
		width: 0;
		flex: 1;
		height: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
		position: relative;
		> image {
			width: 60rpx;
			height: 60rpx;
		}
		.icon {
			position: absolute;
			top: 0;
			left: 50%;
			transform: translateX(-50%);
			transition: 0.5s;
		}
		.icon--active {
			opacity: 0;
			transition: 0.5s;
		}
		> text {
			font-size: 22rpx;
			color: #14131f;
			line-height: 30rpx;
		}
	}
	.active {
		.icon {
			opacity: 0;
		}
		.icon--active {
			opacity: 1;
		}
		> text {
			font-weight: bold;
			color: var(--theme-color);
		}
	}
}
</style>
