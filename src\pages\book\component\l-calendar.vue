<template>
	<view class="calendar">
		<view class="calendar__list">
			<view class="item" :class="{ active: day === item.day }" @tap="onChangeDate(item.day)"
				v-for="(item, index) in latelyEnterDay" :key="index">
				<view class="date">{{ item.text }} </view>
				<uni-dateformat class="date" :date="item.day" format="MM-dd"></uni-dateformat>
				<view class="price">
					<text class="unit">¥</text>
					{{ price }}
				</view>
			</view>
		</view>
		<y-calendar v-model="showMoreDay" :price="price" :date="day"
			:dateRange="dateRange" @onChangeDate="onChangeDate">
			<view class="calendar__more">更多 <br />
				日期</view>
		</y-calendar>
	</view>
</template>
<script setup lang="ts">
import { toRefs, reactive, ref, computed, watch, onMounted } from "vue"
import { hexToRgb, getWeekDate, markdownToHtml, compareTimes } from "@/utils/tool.js"
import dayjs from "dayjs"

const props = defineProps({
	day: {
		type: String,
		default: dayjs().format("YYYY-MM-DD")
	},
	dateRange: {
		type: Array,
		default: []
	},
	price: {
		type: Number,
		default: 0
	},
})

const emits = defineEmits(["update:day"])

// 分时时段
const timeLaws = ref([])
const timePeriodList = ref([])
const timePeriodField = ref({})

const dealTimePeriodDatas = () => {
  // 初始化默认日期下的默认时间段
  const _timeList = (timeLaws.value || []).filter(
    (e) =>
      e?.timeShareData === dayjs(props.day).format('YYYY-MM-DD') &&
      e?.timeShareDateList?.length,
  );
  
  if (_timeList?.length) {
    let _flag = false; // 是否赋值过
    const _list = (_timeList[0]?.timeShareDateList || []).map((_item) => {
      const _disabled = (!dayjs(props.day).isAfter(dayjs()) && (_item?.timeShareEndTime && compareTimes(dayjs().format('HH'), _item?.timeShareEndTime) === 1) || !_item?.stockAmount);
      if (!_disabled && !_flag) {
        timePeriodField.value = _item; // 设置第一个可用的时段为默认值
        _flag = true
      }
      return {
        ..._item,
        disabled: _disabled,
      }
    })
    timePeriodList.value = _list; // 当前选中时间对应的分时时段
  } else {
    timePeriodField.value = {}
    timePeriodList.value = []
  }
}
const ticketDayList = ref<any>([])

// 入园快捷选择
const latelyEnterDay = ref([])

//选择入园时间
const onChangeDate = day => {
	emits("update:day", day)
}

// 入园快捷选择
watch(
	() => props.day,
	(newVal, oldVal) => {
		let [startDate] = props.dateRange || []
		const lastDayList = []
		if (!startDate) startDate = dayjs().format("YYYY-MM-DD")
		console.log(newVal)
		const dayList = []
		for (let index = 0; index < 4; index++) {
			const day = dayjs(startDate).add(index, "day").format("YYYY-MM-DD")
			let text = getWeekDate(day)
			// 今天显示今天
			if (dayjs(day).isSame(dayjs(), "day")) text = "今天"
			// 明天显示明天
			if (dayjs(day).isSame(dayjs().add(1, "day"), "day")) text = "明天"
			console.log({
				text,
				day
			})
			dayList.push({
				text,
				day
			})
		}
		// 如果当前选择的日期不在快捷选择中、且在于有效时间内，则添加到快捷选择中
		if (
			!dayList.some(item => item.day === newVal) &&
			dayjs(newVal).isAfter(startDate)
		) {
			const lastIndex = dayList.length - 1
			dayList[lastIndex] = {
				text: getWeekDate(newVal),
				day: newVal
			}
		}
		console.log(dayList)
		latelyEnterDay.value = dayList
	},
	{
		immediate: true
	}
)

const showMoreDay = ref(false)
</script>
<style lang="scss" scoped>
.calendar {
	display: flex;
	justify-content: space-between;
	align-items: center;

	.calendar__list {
		flex: 1;
		display: flex;
		overflow: scroll;
		-ms-overflow-style: none;
		scrollbar-width: none;

		&::-webkit-scrollbar {
			display: none;
		}
	}

	.calendar__more {
		color: var(--theme-color);
		font-weight: 400;
		font-size: 26rpx;
		width: 90rpx;
		height: 164rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 3;
		box-shadow: -4px 0px 8px -2px rgba(219, 219, 219, 0.5);
	}

	.active {
		background: rgba(var(--theme-bg), 0.1) !important;
		border: 1px solid var(--theme-color) !important;
	}

	.item {
		flex: none;
		position: relative;
		display: flex;
		align-items: center;
		flex-direction: column;
		justify-content: center;
		padding: 0 15rpx;
		overflow: hidden;
		width: 136rpx;
		height: 164rpx;
		margin-right: 16rpx;
		border-radius: 8rpx;
		background: #fbfbfb;
		border: 1px solid #e1e1e2;

		.date {
			font-size: 25rpx;
			font-weight: 400;
			color: #050505;
			line-height: 38rpx;
		}

		.price {
			font-size: 32rpx;
			font-weight: 500;
			color: #f43636;
			line-height: 40rpx;

			.unit {
				font-size: 24rpx;
			}
		}
	}
}
</style>
