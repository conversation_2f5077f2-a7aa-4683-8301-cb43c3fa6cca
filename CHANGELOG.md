# Changelog

All notable changes to this project will be documented in this file.

## [unreleased]

### 🚀 Features

- 添加店铺信息支持，更新登录页面展示逻辑和工具函数
- 在订单合并数据中添加系统来源支持
- 提交分支名,提交信息，提交信息管理议题id功能 #221
- 更新 Vite 配置文件中的端口号为 1998，并修改提交信息脚本中的项目路径为 scenic/3-yilvtong。  #226
- 添加全局悬浮按钮及拖拽功能，优化用户交互体验 #258
- 替换全局悬浮按钮为返回原系统按钮，优化样式和交互体验 #258
- 重构 App.vue，优化全局返回按钮的创建逻辑，动态加载 vConsole，提升用户体验 #252
- 添加微信登录处理逻辑，优化 App.vue 中的初始化流程 #252
- 优化 App.vue 中的系统来源参数处理，改进返回按钮逻辑，使用解码后的 source_url 进行跳转 #252
- 更新 scenic.vue 以支持不同来源类型的展示逻辑，添加 scenicMap 组件并调整 sourceType 处理 #256
- 更新 scenicMap 组件，添加地图加载逻辑和边界处理，优化 scenic.vue 中的样式和布局 #256
- 调整 scenic.vue 和 scenicMap.vue 中的高度计算，优化布局以适应不同屏幕 #256
- 在 pages.json 中添加 scenicByTravelai 页面配置，更新导航栏标题 #257
- 新增分支名和提交信息检查功能，优化提交信息管理，添加 husky 钩子脚本以验证提交信息和分支命名规范。 #265
- 新增分支名和提交信息检查功能，优化提交信息管理，支持合并提交信息解析和议题ID提取 #265
- 在 scenicMap 组件中添加点位弹窗功能，优化地图点位聚合和距离更新逻辑，增强用户交互体验 #257
- 修改登录页自动登录逻辑，使用 expire 替代 isAutoLogin 变量 #255
- 添加 AI 页面并更新聊天组件逻辑 #255
- 升级新版ai聊天界面 #255
- 更新环境配置，调整 AI 基础 URL 以简化路径并修复组件导入路径 #255
- 更新 AI 页面逻辑，添加天气信息获取功能并优化 API 路径 #255
- 在 AI 聊天组件中添加 sceincName 参数支持，优化页面跳转逻辑并删除不必要的样式文件 #255
- 移除 scenicMap 组件中点位点击事件处理逻辑，点击点位时不再执行任何操作 #257
- 优化 scenic.vue 和 scenicByTravelai.vue 组件，移除条件渲染和调整样式 #257
- 在多个组件中添加 scenicId 属性，优化页面跳转逻辑并引入自定义点位列表功能 #258
- 移除 y-chat 组件中的过渡效果，简化结构 #258
- 在 scenicMap 组件中添加页面显示时地图重新初始化的逻辑，优化地图管理 #275
- 优化地图边界计算逻辑，增加边界扩展功能以提升用户体验 #256
- 优化 scenicMap 组件中的地图和聚合点管理逻辑，添加销毁实例和清理全局变量的功能，以提升性能和用户体验 #258
- 优化 App.vue 中的系统来源参数获取逻辑，支持从路由参数和本地缓存读取 #291
- 在 createBackButton 函数中添加延迟隐藏文本和调整样式的功能，以提升用户体验 #291
- 修改 dialogue.vue 组件，调整 flagList 属性类型为数组并更新默认值，同时在内容处理逻辑中添加换行符转换为 HTML 标签的功能 #291
- 所有环境的天气接口使用同一个 #255
- 在 .env.canary 文件中添加 AI 相关的 URL 配置，包括基础 URL、聊天、语音识别、文本转语音和路线规划服务的 WebSocket 地址 #258
- 更新 AI 组件中的头像图片和样式，调整设置组件的边框样式，优化 scenicByTravelai.vue 中的间距和布局 #281 #279 #278 #277
- 修改个人中心页面的导航逻辑，使用 Tool.goPage.push 替代 uni.navigateTo，并修复手机号绑定逻辑中的条件判断，确保正确性 #271
- 在日历组件中添加价格列表支持，更新价格显示逻辑，确保选择日期时能正确获取对应价格 #336

### 🐛 Bug Fixes

- 测试
- 在下单页添加对权益卡的支持
- 调整 scenicByTravelai.vue 中的 marginTop 样式，简化条件逻辑以提升代码可读性 #258
- 修复ESLint错误

### 📚 Documentation

- Auto-generate changelog [ci-skip] #106

## [release-v4.2.0] - 2025-04-03

### 🚜 Refactor

- *(y-magiccube)* 避免直接修改原始对象，使用新对象进行操作
- 统一网关，更新API基础路径并移除无用配置  #XINFANSHI-836

## [release-v4.1.1] - 2025-03-21

### 🐛 Bug Fixes

- #CSZ-341 修复tabBar的问题

## [release-v4.1.0] - 2025-03-11

### 🚀 Features

- 优化初始化逻辑
- Add tke
- 没有店铺自动跳转默认店铺
- 金额计算优化
- 添加生产默认店铺ID
- 权益卡审核跳转优化
- 权益卡列表添加关键字搜索
- Debug
- 仅限本人购买的票下单时自动回填
- 人脸设别添加轮训验证
- 票列表有效日期长存
- 今日不可入园的票不可选今天日期
- 删除无用的登录页
- 添加  vscode 插件建议
- 添加 unocss
- 添加缺省页
- New feat
- 将拖拽组件封装成hook
- Refactor dialogue component styles
- 首页
- 活动资讯
- 攻略游记
- 意见反馈
- 购买验证该实名是否已下单
- 添加 debug 功能
- 添加请求唯一标识符
- 修复票号过长不换行的问题
- 添加票二维码组件
- 更新二维码组件
- 开发人脸补录功能
- 添加人脸提示弹窗功能
- 修复人脸提示弹窗显示问题
- 人脸录入提示弹窗改成阻塞
- 订单详情页，实名信息有人脸显示人脸
- 优化联系人列表页面加载性能
- 移除订单号显示
- 优化初始化数据获取逻辑
- 全局替换用户信息跟实名信息获取方式
- Ai 咨询聊天页
- Ai咨询页文章推荐跟商品推荐
- Ai域名更改
- Ai咨询页优化
- 添加默认店铺跳转
- 设计器商品列表改动
- 优化初始化加载
- 行程偏好优化，前文提及的内容不在出现在偏好中
- 易旅宝-门票预定-景区列表-商品列表兼容分时开发
- 景区门票购买兼容分时时段
- 门票列表--组合票兼容分时
- Ai
- Ai导览升级优化
- Ai 导览跟ai行程兼容
- 请求函数添加ignoreStatusCode参数
- 登录页、忘记密码主题功能
- 优化渲染高亮逻辑
- 行程规划新增展开收起，tutu的气泡样式优化
- 修复登录失效时，没有正确跳转到登陆页的问题
- 替换 GitLab CI 配置为 Jenkinsfile 以支持新的构建和部署流程
- 添加区块链证书页面及订单详情跳转功能
- 添加证书下载功能及动画效果
- 优化证书下载功能，使用新方法打开下载链接
- 优化联系人和票务信息组件，简化代码结构和提升可读性
- 添加票务数量选择组件，优化订单信息页面的交互体验
- 优化套票信息组件，添加实名制支持及集合票处理逻辑
- 优化套票信息组件，简化代码结构并提升可读性
- 优化票务日期处理逻辑，根据集合时间开关动态获取游玩日期

### 🐛 Bug Fixes

- 区块链交易展示
- Test
- Bug
- 添加浏览器导航栏
- Test 订单列表票数
- *(订单列表)* 显示数量
- 修复同一订单中的两张门票的二维码一样
- 修复退单问题
- 修复头像不显示问题
- 订单详情多个供应商bug
- 票详情票号展示 bug
- 订单列表页的组合票添加票名称
- 微信端去除导航
- 支付回调慢，增加等待状态
- 去除接收方区块链账号
- 分时下单 bug
- 修复非分时没库存bug
- 下单添加 isComposeSimpleGood 字段
- 下单接口删减字段
- 重构
- 重构审核相关逻辑
- 修改文案
- 出票规则字段修改
- Prod
- 优化待支付倒计时
- 权益卡/票下单时添加字段
- 购买权益票添加身份证字段
- 下单添加实名校验
- 优化下单前的状态判断
- Merge branch 'canary' into dev
- 修复刷新页面时报错问题
- 统一获取路由参数的方式
- 支付去除检测实名的验证
- 添加 canary 环境 loginUrl 环境变量
- 将项目改造成 cli 模式
- 替换环境变量
- 修复检票二维码
- 退票
- 二维码添加判断
- 重构下单选择日期代码
- 统一返回字段 list 为 data
- 权益票不可选择数量
- 优化下单页传参
- 修复下单添加按钮无法添加问题
- 修复退单报错
- 优化重定向跳转
- 重定向
- 修复环境变量 VITE_APPID 读取不到的问题
- 优化联系人提示
- 修复退票勾选问题
- Merge branch 'dev' into test
- 权益卡隐藏退票按钮
- 解决精度问题
- 升级 dcloudio
- 移除 unocss
- 解决收藏页面渲染问题
- 智慧导览卫星图、缩放工具、用户定位、天气
- 位置监听
- 定位精度范围
- 聚合点位
- 自定义聚合点位
- 聚合点位性能优化
- 交互点位持久化
- 点位弹窗
- 事件交互解耦
- 弹窗层级、交互解耦
- 导览列表、Tab
- 聚合样式算法、点位详情、路线列表、路线详情
- 路线详情、路线地图、聚合样式
- 路线弹窗、点位导航
- 位置点位、导航
- 罗盘功能、航线实例优化
- 音频播放、点位类型枚举
- 搜索
- 菜单图标
- 区分点位类型、弹窗
- 优化 tutu 拖动效果
- 处理录音静默时间问题
- Dev
- 第三方地图导航、播放器 bug、点位背景
- 点位详情吸附
- 播放器
- 点位弹窗指令
- 详情轮播
- 路线详情、菜单交互
- 缓存解析
- 罗盘定位
- 点位列表
- 修改权益卡分页列表字段
- Tab 交互样式、全局默认图片
- 定位超时
- 定位调试
- 播放时长、推荐距离
- 无图点位不可点击、tab 容差
- Icon 大小
- Svg 矢量图
- 屏蔽 AI
- 第三方导航调试
- 公众号安全域名配置文件
- 暂存
- 更改权限卡显示错误
- 注销首页搜索
- 修复不过
- 修复样式
- 阅读量
- 外链
- 外链增加阅读量
- 样式修复
- 修改图片样式
- Z- index
- Z-index
- Markdown
- Merge canary
- Merge
- 地图同步加载、点位弹窗指令、直线距离
- 聚合算法、搜索算法、移除防抖
- 点位排序
- 路线点位、地图点位距离异步更新
- 导览新列表、聚合防抖、聚合动画
- 按需计算点位距离
- 更新默认矢量图
- 首页tab缓存所有请求
- 修改枚举值
- 下单实名验证修复
- 移除defineProps导入
- 导航栏
- 地图层级
- 移除https
- 修复新增意见反馈缺少picture参数
- 进入景区范围提示
- 天气矢量图标
- 点位弹窗层级、优化直线距离
- 更新景区信息接口
- 音频播放器
- *(2984)* 修复意见反馈图片上传样式
- 优化点位搜索算法、智能讲解
- 全局音频销毁
- 智能讲解触发同步
- 微信签名
- 签名调试
- Jssdk
- IsJWeixin
- 打开微信位置
- 打开微信位置 jWeixin
- Flex-end
- 路线详情交互动画
- 动画时长
- 天气位置
- 导航拦截
- 路线点位定位
- 添加剩余次数
- GuideId
- 修改权益卡过期判断
- *(2908)* 优化样式
- 修复下单成功后不跳转
- Svg 插件
- 动态渲染 tab
- 店铺导航
- 店铺导航支持自定义图标
- Svg 插件 path
- 新增 tab 路由组件
- Tab
- 链接路由
- 导航页面路由
- 链接设置 - 自定义链接
- 单票、组合票、权益卡详情链接
- Svg 调整
- 图文导航样式调整
- 样式调整
- 图文导航指示器
- Refactor orderDetailFooter.vue and refund.vue components
- 导航栏主题、商品组件
- Tab 组件区域滚动、上拉加载
- 活动页
- 准生产环境图床环境变量
- ReturnableQuantity
- 添加订单状态
- 修复活动资讯类型异常
- Update logo
- 解决标签样式问题
- NFT 标签
- NFT 标签、订单发行信息
- 智旅链查询
- 发行信息
- 1
- 溯源记录
- 智旅链链接
- 组合票门票溯源
- 优化订单部分
- 解决单票实名与非实名展示bug
- 修复订单详情页跳转问题
- 修复票二维码组件状态判断错误的问题
- 修复订单详情页剩余时间显示错误的问题
- 修复购买日期选择错误的问题
- 修复二维码组件的值绑定问题
- 修复二维码覆盖层层级问题
- 修复人脸跳转穿透问题
- 修复订单详情页的人脸跳转按钮传参
- Canary 区块链链接
- UI
- 修复身份证信息显示问题https://git.shukeyun.com/scenic/workflow/-/issues/3909
- 修改已核销次数显示位置
- 更新产品下拉列表接口
- 解决初始化loading问题
- 替换退票接口
- 修复退票的请求方式
- 优化登录问题
- AI 行程规划
- 行程规划
- Pull test
- 修复跳转问题
- 聊天框markdown样式优化、语音播放功能
- 增加回复中不可提问的限制
- 优化录音体验，松开按钮停止录音，识别完成发送
- 连接中不可发送消息
- 地点导航规划
- 修复序号异常
- 优化录音各种异常处理
- 行程状态管理
- 只读交互
- 优化录音不给
- 行程按钮展示逻辑优化
- 解决推荐商品无法跳转问题
- 修复语音播放bug
- 商品推荐
- Npm
- 格式化导航信息
- Yml
- UI 修复
- Del
- 全局更换跳转方法
- Merge branch 'canary' into test
- 登录页UI
- 优化ai咨询页的历史记录
- 文章推荐
- 导航
- 空点位数据展示
- 优化语音播放打断问题
- 添加断线重连功能
- 行程交互
- 文章跳转
- 局部滚动
- 移除打印信息
- 行程地图
- 更改setCookie接口
- 解决公用方法 getRoute 问题
- Merge branch 'feat/debug' into test
- AI 行程详情
- AI 行程空指针异常
- 数据异常处理
- 简介展开按钮
- 提示语间距
- 添加按钮样式
- 屏蔽弹窗拖拽
- Merge remote-tracking branch 'origin/master' into canary
- 优化行程偏好显示效果
- 优化ai咨询会话
- 解决高亮渲染错乱问题
- 修复行程规划按钮显示问题
- 代码优化
- 修复分时时段设置库存的问题
- 修复分时时段置灰逻辑
- 修复组合票置灰的逻辑
- 修复分时预约没时段的库存问题，更改实名制的取值字段
- 解决语音进度条问题
- 语音导览ui优化
- 文案更改
- 解决音频使用体验
- 导览路线、语音枚举
- 更换 AI 生产域名
- 屏蔽 Trace
- 更换 ws 生产域名
- 聚合级别
- 修复购买非本人购买权益卡下单的问题
- 修复 AI 路线按钮异常
- 行程优化
- 区分点位路线
- 行程窗口拖动
- 行程导航
- Wx login
- Wxlogin
- 微信登录参数设置为自动登录
- 进入登录页添加判断，是否有cookie，有直接跳转首页
- 修改字段
- 解绑微信
- 绑定微信接口替换
- 优化绑定微信接口
- 调试绑定微信接口
- Tutu 提供默认插槽
- 行程智能生成
- 拖动排序
- 点位描点定位
- Merge branch 'test' into canary
- Ai 弹窗固定两个位置
- 增大ai弹窗拖动的触发范围
- 继续增大ai弹窗拖动的触发范围
- 解决音频选择 bug
- 行程点位列表样式布局调整，修复文章推荐跳转异常
- 优化dialogue，删除不必要代码
- 修复tutu不能滚动查看历史记录的问题
- 提交
- 解决录音未开启成功，页面报错问题
- 修改
- 修复苹果不兼容的问题
- 优化一下登陆失效逻辑
- 解决登录重定向问题
- 修复tutu回复过程中，向上滚动值有偏差造成自动停止的问题
- 加个提示
- Ai回复终极优化
- 优化滚动
- 配置404页面
- 修复智旅链交易详情地址变化的问题
- 修复购买门票时，购买数量和库存数量对不上的问题
- 订单详情 组合票 一票多人开发
- Debugh
- 处理人脸图片链接问题

### 💼 Other

- 添加无店铺错误页
- 修改订单相关UI
- 修复人脸图片不回显问题

### ⚙️ Miscellaneous Tasks

- 添加自动导入组件功能
- 调整二维码尺寸和页面高度
- 调整单个门票组件样式
- 添加登录页、个人中心页
- 删除忘记密码页面和相关组件
- 调整页面初始化逻辑和重定向处理
- Optimize build process by enabling console log removal during minification
- 修复登录和加载页面的重定向逻辑
- 优化页面加载性能和重定向逻辑
- 添加页面loading动效，优化逻辑
- 修改过度时间

## [jaffrey-test] - 2022-09-20

### 🚀 Features

- Init
- Add login page
- 登录页
- 添加跳转方法 goPage，添加店铺 storeId
- 新加上传图片组件
- 订单详情背景主题
- 新增日历选择器组件
- 解决小程序无法携带cookie问题
- 新增退出登陆功能
- 解决跨域问题
- 添加缺省组件，修改一些样式
- 优化 tabBar 样式
- 电子票样式
- 添加导航组件
- 新加【加载更多】组件
- 订单加载更多
- 新增首页轮播图
- 优化票样式
- 添加 markdown 解析器
- 新加收货地址功能
- 个人中心新功能
- 微信解绑，绑定功能
- 添加枚举值 团体票
- 添加枚举 orderStatus
- 弹窗确认是否换绑手机号
- 微信换绑手机号确认弹窗

### 🐛 Bug Fixes

- H5
- Home
- Home-ticket
- Merge branch 'feat/merge' into deng
- 优化登陆模块
- Tarbar
- 优化上传图片组件
- 登陆页样式
- 优化兼容小程序
- @tab
- 优化样式
- 兼容微信获取路由参
- Merge branch 'deng' of git.shukeyun.com:scenic/shop into feat/merge
- 优化轮播图
- 优化tabbar显示隐藏逻辑
- 修复一些bug，优化一些样式
- Pull merge
- 联系人
- 组合退票
- 审批
- 景区轮播图传数据
- 人脸识别
- 服务条款
- 条款勾选样式
- 组合订单展示+下单加上代理商
- Test打包
- 实名认证加上点击
- 实名认证手机号检验
- 组合旅游标签标签修改
- 订单检票规则
- Ios时间戳
- Merge branch 'feat/deng' of git.shukeyun.com:scenic/shop into merge
- 退票状态
- Bug test
- 实名去空格 优化
- 审批细化  票数区间
- Test appid
- Text appid
- Text 添加logOut
- 去除审批方式判断
- IsPeopleNumber
- 添加单票审批提示
- 拉取test 打包
- 修改 bug
- 优化跳转登录页逻辑
- 优化常用联系人跳转问题
- 价格策略固定传值 1
- 修改审批判断逻辑
- 旅游卡时间计算
- 旅游卡名称
- 套票新增控制修改
- Merge test
- 组合联系人 分时预约
- 旅游卡默认数 ，最大库存限制
- 推荐景点单票
- 修复票状态枚举
- Build
- 出库失败
- SystemType
- 参数数组问题

### 💼 Other

- Uni_modules
- Merge remote-tracking branch 'origin/deng' into feat/merge
- 部署dev
- 部署 test

### 🎨 Styling

- 新加门票预定页
- 订单详情页
- 票样式
- 优化 UI
- 样式
- 二维码的样式
- 优化样式问题

### ⚙️ Miscellaneous Tasks

- Delete .vite
- Delete .hbuilderx
- Update .gitignore
- 配置环境变量

<!-- generated by git-cliff -->
