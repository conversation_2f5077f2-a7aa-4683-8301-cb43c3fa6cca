<template>
	<div class="indexPage" ref="indexPageRef">
		<view class="page"> <component :is="componentObj[curPage]" /></view>
		<tabBar v-model="curPage" />
		<view class="ifX"> </view>
	</div>
</template>

<script setup>
import { ref, onBeforeMount, defineAsyncComponent } from "vue"
import tabBar from "./component/tabBar.vue"
import { getRoute } from "@/utils/tool.js"
const indexPageRef = ref(null)
// setTimeout(() => {
// 	console.log(
// 		"indexPageRef.value.style.opacity = 1indexPageRef.value.style.opacity = 1",
// 		indexPageRef.value
// 	)
// 	indexPageRef.value.style.opacity = 1
// }, 1000)

const componentObj = {
	home: defineAsyncComponent(() => import("@/pages/home/<USER>")),
	tour: defineAsyncComponent(() => import("@/pages/tourList/tourList.vue")),
	order: defineAsyncComponent(() => import("@/pages/order/order.vue")),
	my: defineAsyncComponent(() => import("@/pages/my/my.vue")),
	travelCardList: defineAsyncComponent(() =>
		import("@/pages/travelCardList/travelCardList.vue")
	),
	ticketList: defineAsyncComponent(() =>
		import("@/pages/ticketList/ticketList.vue")
	)
}
const curPage = ref("home")
onBeforeMount(() => {
	const curTab = getRoute.params().curTab
	if (curTab) curPage.value = curTab
})
</script>

<style lang="scss" scoped>
.indexPage {
	opacity: 1; /* 初始透明度设置为 0 */
	transition: opacity 0.2s ease-in-out; /* 设置过渡效果，持续时间为 1 秒 */
	width: 100%;
	height: 100vh;
	display: flex;
	flex-direction: column;
	.page {
		flex: 1;
		overflow: scroll;
		padding-bottom: 98rpx;
	}
	.ifX {
		height: constant(safe-area-inset-bottom);
		height: env(safe-area-inset-bottom);
		background-color: #fff;
	}
}
</style>
