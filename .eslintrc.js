module.exports = {
  root: true,
  env: {
    browser: true,
    node: true,
    es2021: true,
  },
  parserOptions: {
    ecmaVersion: 2021,
    sourceType: "module",
    parser: "@babel/eslint-parser",
    requireConfigFile: false,
    ecmaFeatures: {
      jsx: true,
    },
  },
  plugins: ["vue"],
  extends: ["eslint:recommended", "plugin:vue/vue3-essential"],
  rules: {
    "no-console": "off",
    "no-debugger": process.env.NODE_ENV === "production" ? "warn" : "off",
    "vue/multi-word-component-names": "off",
    "vue/html-self-closing": "off",
    "vue/require-default-prop": "off",
    "vue/no-v-html": "off",
    "no-undef": "off",
    "no-unused-vars": "off",
    "parser-error": "off",
    "@typescript-eslint/no-parsing-error": "off",
    // 禁用所有可能的解析错误
    "babel/no-invalid-this": "off",
    "babel/quotes": "off",
    "babel/semi": "off",
    "babel/no-unused-expressions": "off",
    // 添加一个通配符规则
    "*": "off"
  },
  globals: {
    // uni-app 全局变量
    uni: true,
    wx: true,
    plus: true,
    getApp: true,
    getCurrentPages: true,
    Tool: true,
    dayjs: true,
  },
  overrides: [
    {
      files: ["src/utils/tools.ts"],
      parser: "espree",
      rules: {
        "no-parsing-error": "off"
      }
    }
  ]
};
