<template>
	<view class="ver-code" :class="disableBtn ? 'text-color-grey' : 'text-color-assist'" @tap="setVerCodeStatus.start()">
		{{ setVerCodeStatus.verText }}</view>
</template>

<script setup>
import { ref, toRefs, reactive, computed, watch } from 'vue'
import { getEnv } from "@/utils/getEnv";
import request from '@/utils/request.js'

const { VITE_IMG_HOST, VITE_UPLOAD_HOST } = getEnv();
const imgHost = ref(VITE_IMG_HOST);
const props = defineProps({
	// 手机号
	mobile: {
		type: String,
		default: ''
	},
	//限制张数
	limit: {
		type: Number,
		default: 1
	},
	//是否具有上传功能
	upload: {
		type: Boolean,
		default: false
	}
})

const setVerCodeStatus = reactive({
	verCount: 0,	// 倒计时
	verText: '获取验证码',
	async start() {
		if (this.verCount !== 0) return
		this.verCount = 60
		const done = await getVerCode()
		if (done) {
			const timer = setInterval(() => {
				this.verText = `倒计时 ${this.verCount--} s`
				if (this.verCount <= 0) {
					this.verText = '重新获取'
					clearInterval(timer)
				}
			}, 1000)
		} else {
			this.verCount = 0
		}
	}
})


const disableBtn = computed(() => {
	return !props.mobile
})

//获取验证码
const getVerCode = async () => {
	console.log(disableBtn.value);
	if (disableBtn.value) return

	const params = {
		credential: props.mobile,
		type: 2		// 1 邮箱 2 手机号
	}
	uni.showLoading({
		title: '发送验证码'
	})
	try {
		console.log('try');
		await request.casPost('/otp', params)
		uni.hideLoading()
		uni.showToast({
			title: '验证码发送成功',
		})
		return true
	} catch (e) {
		uni.hideLoading()
		uni.showToast({
			title: '验证码发送失败',
			icon: 'error'
		})
		return false
	}
}

const aaa = ref('dd')
const emits = defineEmits(['update:modelValue'])

let imgList = ref([]);
watch(() => props.modelValue, (newVal) => {
	console.log('newvalllll');
	console.log(newVal);
	imgList.value = newVal ? newVal.split(',').map(e => { return imgHost + e }) : [];;
	console.log('imgList.value', imgList.value);
})

//显示隐藏上传按钮
const showUploadBtn = computed(() => {
	//长度
	const isLength = imgList.value.length < props.limit
	return isLength && props.upload
})

//选择图片上传
const chooseImage = () => {
	uni.chooseImage({
		count: 1, //默认 9
		sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有
		sourceType: ['album'], //从相册选择
		success: function (res) {

			const tempFilePaths = res.tempFilePaths;
			uni.showLoading({ mask: true });
			const uploadTask = uni.uploadFile({
				url: VITE_UPLOAD_HOST,
				filePath: tempFilePaths[0],
				name: 'file',
				success: (uploadFileRes) => {
					const imgObj = JSON.parse(uploadFileRes.data)[0]
					console.log('im gob,', imgObj);
					console.log(props.modelValue);
					const newList = props.modelValue ? props.modelValue + ',' + imgObj.path : imgObj.path
					console.log(newList);
					emits('update:modelValue', newList)
					uni.hideLoading()
				}
			})
			// uploadTask.onProgressUpdate((res) => {
			// 	aaa.value = '上传进度' + res.progress
			// 	console.log('上传进度' + res.progress);
			// 	console.log('已经上传的数据长度' + res.totalBytesSent);
			// 	console.log('预期需要上传的数据总长度' + res.totalBytesExpectedToSend);

			// 	// 测试条件，取消上传任务。
			// 	if (res.progress > 50) {
			// 		uploadTask.abort();
			// 	}
			// });
		}
	});
}
//删除图片
const delImage = (index) => {
	let newList = JSON.parse(JSON.stringify(props.modelValue.split(',')))
	newList.splice(index, 1)
	newList = newList.join(',')
	console.log('delImage');
	console.log(newList);
	emits('update:modelValue', newList)
}
//预览图片
const previewImage = (index) => {
	uni.previewImage({
		current: index,
		urls: imgList.value
	})
}
</script>

<style lang="scss" scoped>
.ver-code {
	font-size: 35rpx;
	width: 250rpx;
	text-align: right;
}
</style>