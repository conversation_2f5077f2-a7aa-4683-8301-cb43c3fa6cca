<template>
	<view class="add-number">
		<view class="subtract" :class="{ disable: disSubtract }" @tap="onChangeTicketNum('subtract')">
			<view class="across"></view>
		</view>
		<view class="number">
			{{ modelValue }}
		</view>
		<view class="add" :class="{ disable: disAdd }" @tap="onChangeTicketNum('add')">
			<view class="across"></view>
			<view class="vertical"></view>
		</view>
	</view>
</template>
<script setup lang="ts">
import { toRefs, reactive, ref, computed, watch, onMounted } from "vue"
import { hexToRgb, getWeekDate, markdownToHtml, compareTimes } from "@/utils/tool.js"
import request from "@/utils/request.js"
import dayjs from "dayjs"

const props = defineProps({
	modelValue: {
		type: Number,
		default: 1
	},
	numRange: {
		type: Array,
		default: [1, 99]
	},
})

const emits = defineEmits(["update:modelValue"])

// 禁用减少按钮
const disSubtract = computed(() => {
	return props.modelValue <= props.numRange[0]
})
// 禁用增加按钮
const disAdd = computed(() => {
	return props.modelValue >= props.numRange[1]
})
// 修改票的数量
const onChangeTicketNum = type => {
	let num = props.modelValue
	if (type === "add" && !disAdd.value) {
		num++
	} else if (type === "subtract" && !disSubtract.value) {
		num--
	}
	emits("update:modelValue", num)
}

</script>
<style lang="scss" scoped>
.add-number {
	display: flex;
	align-items: center;

	.across,
	.vertical {
		position: absolute;
		width: 22rpx;
		height: 4rpx;
		background-color: #fff;
		border-radius: 2rpx;
	}

	.add,
	.subtract {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 50rpx;
		height: 50rpx;
		background: var(--theme-color);
		border-radius: 8rpx;

		&.disable {
			background-color: #f0f0f0;

			.across,
			.vertical {
				background-color: #c5c5c5;
			}
		}
	}

	.vertical {
		transform: rotate(90deg);
	}

	.number {
		margin: 0 30rpx;
	}
}
</style>
