<template>
  <view
    :style="{
      margin: `0 ${conf.pageMargin}`,
    }"
  >
    <view
      v-if="conf.type === 'blank'"
      :style="{
        height: conf.height,
      }"
    >
    </view>
    <view
      v-else
      :style="{
        margin: `${conf.height} ${conf.pageMargin}`,
        borderTop: `1rpx ${conf.pattern} ${conf.color}`,
      }"
    />
  </view>
</template>

<script setup>
import { ref, toRefs, computed } from "vue";
import { pxToRpx } from "@/utils/tool.js";
const props = defineProps({
  config: {
    type: Object,
    default: {},
  },
});

const conf = computed(() => {
  const style = {};
  const { type, blankHeight, pageMargin } = props.config;
  if (blankHeight) {
    style.height = `${pxToRpx(blankHeight)}`;
  }
  // 页面间距
  if (pageMargin) {
    style.pageMargin = `${pxToRpx(pageMargin)}`;
  }
  return {
    ...props.config,
    ...style,
  };
});
</script>

<style lang="scss" scoped></style>
