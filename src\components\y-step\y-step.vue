<template>
	<view class="step-box">
		<template v-for="(item, index) in stepContent">
			<view class="step-item" :class="{'step-item-active': curStep > index}">
				<view class="complete">
					<view class="number">
						{{index+1}}
					</view>
				</view>
				<view class="text">
					{{item}}
				</view>
			</view>
			<view v-if="index < stepContent.length-1" class="line" :style="{opacity: curStep > index+1 ? 1 : 0.06}"></view>
		</template>
	</view>
</template>

<script>
	export default {
		name:"step",
		props: {
			curStep:{
				type: Number,
				default: 0
			},
			stepContent: {
				type: Array,
				default: []
			}
		},
		
	}
</script>

<style lang="scss" scoped>
	.step-box{
		display: flex;
		justify-content: space-around;
		align-items: center;
		.line{
			width: 28%;
			height: 4rpx;
			border-radius: 3rpx;
			background: linear-gradient(90deg, var(--theme-color) 0%, rgba(48, 140, 138, 0) 100%);
		}
		.step-item{
			position: relative;
			display: flex;
			flex-direction: column;
			align-items: center;
			// .complete{
				// 	display: flex;
				// 	align-items: center;
				// 	justify-content: center;
				// 	width: 82rpx;
				// 	height: 82rpx;
				// 	background-color: rgba(48, 140, 138, 0);
				// 	border-radius: 50%;
				// }
				.number {
					background-color: var(--theme-color);
					opacity: 0.4;
					border-radius: 50%;
					color: #fff;
					width: 44rpx;
					line-height: 44rpx;
					text-align: center;
					font-size: 28rpx;
				}
			
				.text {
					color: #000;
					font-size: 24rpx;
					line-height: 40rpx;
					position: absolute;
					bottom: 0;
					left: 50%;
					width: 80px;
					transform: translate(-50%, 24px);
					text-align: center;
				}
			}
			
			.step-item-active {
			
				// .complete{
				// 	display: flex;
				// 	align-items: center;
				// 	justify-content: center;
				// 	width: 82rpx;
				// 	height: 82rpx;
				// 	background-color: rgba(48, 140, 138, 0.09);
				// 	border-radius: 50%;
				// }
				.number {
					background-color: var(--theme-color);
					opacity: 1;
				border-radius: 50%;
				color: #fff;
				width: 44rpx;
				line-height: 44rpx;
				text-align: center;
				font-size: 24rpx;
			}
			.text{
				color: #000;
				font-size: 28rpx;
				line-height: 40rpx;
			}
		}
	}
</style>