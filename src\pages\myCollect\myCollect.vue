<template>
	<y-nav-bar backgroundColor="#7ED3DA" backBtn solid>我的收藏</y-nav-bar>
	<view class="ticket-list-page">
		<view
			class="list"
			:style="{
				borderTopLeftRadius: cityListVisible || labelVisible ? '0' : '60rpx'
			}">
			<l-tab-bar
				@tabClick="tabClick"
				:activeTab="activeTab"
				:tabList="state.tabList" />
			<template v-if="state.ticketList.length !== 0">
				<template v-for="(item, index) in state.ticketList" :key="index">
					<!-- 权益卡 -->
					<y-travel-card
						:ticket="item"
						v-if="activeTab == 2"
						@tap="toBuy(item)" />
					<!-- 单票 组合票 -->
					<y-ticket
						:ticket="item"
						:mold="activeTab"
						v-else
						@tap="toBuy(item)" />
				</template>
				<y-loadmore :status="loadStatus"></y-loadmore>
			</template>
			<template v-else>
				<y-empty class>暂无内容</y-empty>
			</template>
		</view>
	</view>
</template>

<script lang="ts" setup>
import { reactive, ref, toRefs, onBeforeMount, shallowRef, watch } from "vue"
import request from "@/utils/request.js"
import { getRoute } from "@/utils/tool.js"
import { onLoad, onReachBottom } from "@dcloudio/uni-app"

const props = defineProps()
const routerParams = reactive({})

onLoad(option => {
	for (let key in option) {
		routerParams[key] = option[key]
	}
	if (routerParams.index && routerParams.index != 3) {
		activeTab.value = routerParams.index
		state.title = state.tabList[routerParams.index]
	}
})
const loadStatus = ref("loadmore")
//页数
const page = reactive({
	current: 1,
	pageSize: 10
})
//
const state = reactive({
	ticketList: [],
	title: "列表页",
	cityCode: "",
	tabList: ["景区门票", "组合套票", "权益卡"]
})
//tabClick
const activeTab = ref(0)
const tabClick = async index => {
	activeTab.value = index
	state.title = state.tabList[index]
	await getList(index, "init")
}
//页面跳转
const toBuy = item => {
	if (activeTab.value == 0) {
		Tool.goPage.push(`/pages/scenic/scenic?scenicId=${item.scenicId}`)
	} else if (activeTab.value == 1) {
		Tool.goPage.push(`/pages/scenic/scenic?storeGoodsId=${item.storeGoodsId}`)
	} else if (activeTab.value == 2) {
		Tool.goPage.push(
			`/pages/travelCardDetail/travelCardDetail?productId=${item.productId}&pic=${item.pic}`
		)
	}
}
const hasNextPage = ref(true)
const getList = async (index, type) => {
	try {
		if (!hasNextPage.value && type != "init") return
		switch (type) {
			case "init":
				page.current = 1
				break
			case "next":
				page.current += 1
				break
		}
		uni.showLoading({
			title: "易旅宝",
			mask: true
		})
		loadStatus.value = "loading"
		let list = []
		const { userId } = userData?.userInfo
		const params = {
			relationType: "",
			storeId: getRoute.params().storeId,
			userId
		}
		if (index == 0) {
			//景点门票
			params.relationType = 1
			const { data } = await request.get(`/my/myFavorites`, params)
			list = data.data
				.map(item => {
					return item.scenicInfo
				})
				.filter(e => e !== null)
		}
		if (index == 1) {
			//组合套票
			params.relationType = 2
			const { data } = await request.get(`/my/myFavorites`, params)
			list = data.data
				.map(item => {
					return item.storeGoodsInfo
				})
				.filter(e => e !== null)
		}
		if (index == 2) {
			//权益卡
			params.relationType = 3
			const { data } = await request.get(`/my/myFavorites`, params)
			list = data.data
				.map(item => {
					return item.travelCardInfo
				})
				.filter(e => e !== null)
		}
		console.log("33")
		console.log(list)

		if (type === "init") {
			state.ticketList = list
		} else if (type === "next") {
			if (list.length > 0) state.ticketList = [...state.ticketList, ...list]
		}
		console.log("44")
		hasNextPage.value = list.length >= page.pageSize
		if (hasNextPage.value) {
			loadStatus.value = "loadmore"
		} else {
			loadStatus.value = "nomore"
		}
		console.log("55")

		uni.hideLoading()
	} catch (err) {
		console.log(err)
	}
}
let userData: any = {}
onBeforeMount(async () => {
	userData = await Tool.getUserInfo()
	tabClick(activeTab.value)
})

onReachBottom(() => {
	getList(activeTab.value, "next")
})
</script>

<style lang="scss" scoped>
.ticket-list-page {
	background-color: #fff;
	height: 100%;

	.search {
		position: relative;
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 131rpx;
		padding: 0rpx 40rpx;
		background-color: #7ed3da;
		border-bottom-right-radius: 60rpx;
		transition: 0.2s;

		&.straight {
			border-bottom-right-radius: 0rpx;
		}

		&::after {
			position: absolute;
			bottom: -60rpx;
			left: 0%;
			content: "";
			display: block;
			width: 60rpx;
			height: 60rpx;
			background-color: #7ed3da;
			z-index: 0;
		}

		.seach-input {
			flex: 1;
			display: flex;
			align-items: center;
			// width: 500rpx;
			height: 76rpx;
			padding: 0 23rpx;
			border-radius: 45rpx;
			background: #57c5ce;

			.input {
				color: #fff;
				caret-color: #fff;
			}

			.icon {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 50rpx;
				height: 50rpx;
			}

			.placeholder-style {
				font-size: 26rpx;
				color: #fff;
			}
		}

		.location {
			display: flex;
			align-items: center;
			max-width: 200rpx;
			font-size: 28rpx;
			margin-left: 20rpx;
			text-align: right;
			color: #fff;

			.text {
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
				flex: 1;
				// width: 0;
			}

			.icon {
				margin-left: 12rpx;
			}
		}
	}

	.list {
		position: relative;
		padding: 10rpx 40rpx;
		border-top-left-radius: 60rpx;
		background-color: #fff;
		transition: border-radius 0.5s;
	}
}
</style>
