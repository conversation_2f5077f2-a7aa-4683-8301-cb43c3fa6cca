<template>
	<view class="tourist-info">
		<view class="title">
			<y-font-weight v-if="orderType === 'travel'">使用人</y-font-weight>
			<y-font-weight v-if="orderType === 'single' && !isOwner">{{
				`需填 ${ticketNum} 位游客信息`
}}</y-font-weight>
			<y-font-weight v-if="orderType === 'single' && isOwner">本人信息</y-font-weight>
		</view>
		<view v-if="!isOwner" class="link-man__box">
			<!-- ref="linkmanRef" -->
			<Linkman ref="linkmanRef" @onUpdate="onUpdateLinkMan" v-model:selectedContacts="linkManList"
				:max="ticketNum"></Linkman>
		</view>
		<view :style="isOwner ? 'filter: opacity(0.5);' : ''" v-for="(item, index) in linkManList" :key="index">
			<view class="tourist-item">
				<image v-if="!isOwner" @click="delLinkMan(index)" class="minus" src="@/static/image/minus-circle.png"
					mode="scaleToFill" />
				<view>
					<view class="input-box">
						<view class="name"> 姓&emsp;名 </view>
						<view>{{ item.name }}</view>
					</view>
					<view class="input-box">
						<view class="name"> 身份证 </view>
						<view>{{ desensitization(item.idCard, "idCard") }}</view>
					</view>
					<view v-if="!item.faceImageUrl && faceCheck.onlyFace" class="face-tip">请上传人脸信息，否则影响开闸！</view>
				</view>
				<image v-if="!item.faceImageUrl && faceCheck.isFace" class="face-icon" @click.stop="uploadFace(item)"
					src="@/static/image/order/orderdetal-face-icon.png" mode="widthFix" />
			</view>
		</view>
	</view>
</template>
<script setup>
import { toRefs, reactive, ref, watch, onMounted } from "vue"
import Linkman from "@/pages/book/component/linkman.vue"
name: "tourist-info"
const props = defineProps({
	// 票数
	ticketNum: {
		type: Number,
		default: 1
	},
	// 已选联系人
	modelValue: {
		type: Array,
		default: []
	},
	// 仅限本人购买
	isOwner: {
		type: Boolean,
		default: false
	},
	// 订单类型
	orderType: {
		type: String,
		default: "single"
	},
	ticketInfo: {
		type: Object,
		default: () => ({})
	}
})
const emits = defineEmits(["update:modelValue"])
const linkManList = ref([])
const linkmanRef = ref(null)
// 删除联系人
const delLinkMan = index => {
	linkManList.value.splice(index, 1)
}
const faceCheck = computed(() => {
	const { checkType } = props.ticketInfo
	if (checkType) {
		const isFace = checkType.includes(1)
		const onlyFace = isFace && checkType.length === 1
		return { isFace, onlyFace }
	} else {
		return { isFace: false, onlyFace: false }
	}

})
let userData = {}
onMounted(async () => {
	userData = await Tool.getUserInfo()
	if (props.isOwner) {
		linkManList.value.push({
			name: userData.realNameInfo.idName,
			idCard: userData.realNameInfo.idNumber,
			isLinkMan: false
		})
	}
})

watch(
	() => linkManList.value,
	val => {
		emits("update:modelValue", val)
	},
	{ immediate: true }
)

// 上传人脸
const uploadFace = item => {
	console.log(item)
	let url = "/pages/contactsList/contactsList?page=editPage"
	if (item.id) url += `&id=${item.id}`
	if (item.idCard) url += `&idCard=${item.idCard}`
	Tool.goPage.push(url)
}

const del = index => {
	if (props.modelValue.length > 1) {
		let arr = JSON.parse(JSON.stringify(props.modelValue))
		arr.splice(index, 1)
		emits("update:modelValue", arr)
	}
}
// 更新联系人
const onUpdateLinkMan = () => {
	linkmanRef.value?.init()
}
const onCheckMan = index => {
	let arr = JSON.parse(JSON.stringify(props.modelValue))
	arr.forEach((e, i) => {
		if (i === index) {
			e.isLinkMan = !e.isLinkMan
		}
	})
	emits("update:modelValue", arr)
}
</script>
<style lang="scss" scoped>
.tourist-info {
	background-color: #fff;
	border-radius: 24rpx;
	margin-bottom: 20rpx;
	overflow: hidden;
	> .title {
		padding: 30rpx 0;
		margin: 0 30rpx;
		font-weight: 500;
		color: #050505;
		font-size: 34rpx;
		border-bottom: 2rpx solid rgba(228, 228, 228, 0.4);
	}
	.tourist-item {
		display: flex;
		// flex-direction: column;
		align-items: center;
		position: relative;
		// padding: 0 0 0 70rpx;
		margin: 0 30rpx;
		background-color: #fff;
		overflow: hidden;
		padding-bottom: 20rpx;
		&:last-child {
			border-radius: 0px 0px 24rpx 24rpx;
		}
		&:not(:last-child) {
			border-bottom: 2rpx solid rgb(228, 228, 228, 0.4);
		}
		.minus {
			width: 40rpx;
			height: 40rpx;
			margin-right: 30rpx;
		}

		.input-box {
			display: flex;
			align-items: center;
			margin-top: 30rpx;
			font-size: 28rpx;
			font-weight: 400;
			color: #040404;
			line-height: 28rpx;
			.name {
				margin-right: 40rpx;
			}
			.input {
				flex: 1;
			}
		}
		.linkman {
			margin-left: auto;
			font-size: 26rpx;
			font-weight: 400;
			color: #3c93ff;
			line-height: 37rpx;
		}
		.set-linkman {
			display: flex;
			align-items: center;
			margin-bottom: 40rpx;
			font-size: 24rpx;
			font-weight: 400;
			color: #999999;
			.title {
				margin-right: 10rpx;
			}
		}
		.face-tip {
			margin-top: 15rpx;
			font-size: 24rpx;
			font-weight: 400;
			color: red;
		}
		.face-icon {
			margin-left: auto;
			width: 80rpx;
			height: 80rpx;
		}
	}
	.link-man__box {
		margin: 20rpx 30rpx;
	}
}
</style>
