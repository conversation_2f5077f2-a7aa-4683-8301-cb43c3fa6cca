<template>
  <y-nav-bar backgroundColor="#7ED3DA" backBtn solid>实名认证</y-nav-bar>
  <view class="certification">
    <view class="y-list">
      <view class="y-list-item">
        <view class="y-list-item-left"> 银&ensp;行&ensp;卡 </view>
        <input
          class="y-list-item-input"
          v-model="state.bankId"
          :disabled="state.disabled && state.isautonym"
          maxlength="30"
          type="text"
          placeholder="请输入"
          placeholder-class="placeholder-style"
        />
      </view>
      <view class="y-list-item">
        <view class="y-list-item-left">持&ensp;卡&ensp;人</view>
        <input
          class="y-list-item-input"
          v-model="state.name"
          :disabled="state.disabled && state.isautonym"
          type="text"
          placeholder="请输入"
          placeholder-class="placeholder-style"
        />
      </view>
      <view class="y-list-item">
        <view class="y-list-item-left">身份证号</view>
        <input
          class="y-list-item-input"
          v-model="state.idCard"
          :disabled="state.disabled && state.isautonym"
          maxlength="18"
          type="text"
          placeholder="请输入"
          placeholder-class="placeholder-style"
        />
      </view>
      <view class="y-list-item">
        <view class="y-list-item-left">手&ensp;机&ensp;号</view>
        <input
          class="y-list-item-input"
          v-model="state.mobile"
          :disabled="state.disabled && state.isautonym"
          maxlength="11"
          type="text"
          placeholder="请输入"
          placeholder-class="placeholder-style"
        />
      </view>
    </view>
    <y-button @tap="viewInfo" v-if="!showReal && state.isautonym">查看加密信息</y-button>
    <y-button @tap="hideInfo" v-else-if="showReal && state.isautonym">隐藏加密信息</y-button>
    <Drawer :visible="showInfoDrawer" @update:visible="showInfoDrawer = $event">
      <view class="close" @click="showInfoDrawer = false">
        <uni-icons type="closeempty" size="26" color="#999" />
      </view>
      <view class="infoBox">
        <view class="info_txtA">为保障信息安全，请验证身份</view>
        <view class="info_txtB">账号绑定手机：{{ state.mobile }}</view>
        <y-button style="margin-top: 60rpx" @tap="onceVerify">一键验证</y-button>
      </view>
    </Drawer>
    <Drawer :visible="codeShow" @update:visible="codeShow = $event">
      <view class="close" @click="codeShow = false">
        <uni-icons type="closeempty" size="26" color="#999" />
      </view>
      <view class="codeBox">
        <view class="aa">请输入短信验证码</view>
        <view class="bb">已发送验证码至{{ state.mobile }}</view>
        <view class="flexCode">
          <input class="code_ipt" v-model="verCode" maxlength="6" type="number" placeholder="请输入6位验证码" />
          <button class="btn" @tap="confrimCode">确定</button>
        </view>
        <!-- 修改重新发送区域：添加点击事件和状态控制 -->
        <view
          @tap="resendCode"
          :style="{
            color: isCounting ? '#999' : '#7ED3DA',
            pointerEvents: isCounting ? 'none' : 'auto',
            marginTop: '20rpx',
          }"
        >
          {{ time }}s重新发送
        </view>
      </view>
    </Drawer>
    <view class="agree" v-if="!state.isautonym">
      <y-checkbox :checked="agree" @onCheck="onCheckAutoLogin"></y-checkbox>
      <view class="tip"> 我同意<text class="contract" @tap="Tool.goPage.push(`/pages/registerRule/registerRule`)">《环球雅途易旅宝注册服务条款》</text> </view>
    </view>
    <y-button :disable="!state.disabled" @tap="handleBtnClick" v-if="!state.isautonym">立即认证</y-button>
  </view>
</template>

<script setup>
import { reactive, ref, onBeforeMount, watch } from "vue";
import request from "@/utils/request.js";
import Drawer from "@/components/drawer/drawer.vue";

const showInfoDrawer = ref(false);
const codeShow = ref(false);
const props = defineProps();
const phoneVal = ref("");
const userIdVal = ref("");
const agree = ref(false);
const time = ref(60);
const timer = ref(null); // 定时器ID
const isCounting = ref(false); // 是否正在倒计时
const showReal = ref(false);
const hideInfoData = ref({});
const onCheckAutoLogin = () => {
  agree.value = !agree.value;
};
const viewInfo = () => {
  showInfoDrawer.value = true;
};
const hideInfo = () => {
  showReal.value = false;
  state.bankId = hideInfoData.value.cardNumber;
  state.mobile = hideInfoData.value.cardPhone;
  state.name = hideInfoData.value.idName;
  state.idCard = hideInfoData.value.idNumber;
  state.disabled = true;
  agree.value = true;
  state.isautonym = true;
  uni.setStorageSync("autonym", hideInfoData.value);
};

// 修改onceVerify方法
const onceVerify = async () => {
  try {
    showInfoDrawer.value = false;
    codeShow.value = true;

    // 清除可能存在的旧定时器
    if (timer.value) {
      clearInterval(timer.value);
      timer.value = null;
    }

    const res = await request.get(`/send/code`, {
      phone: phoneVal.value,
      businessId: userIdVal.value,
    });

    startCountdown();
  } catch (err) {
    console.error("发送验证码失败:", err);
    uni.showToast({ title: "网络异常，发送失败", icon: "none" });
  }
};

// 新增：倒计时逻辑
const startCountdown = () => {
  isCounting.value = true;
  time.value = 60; // 重置倒计时
  // 清除原有定时器（防止叠加）
  if (timer.value) clearInterval(timer.value);

  timer.value = setInterval(() => {
    if (time.value > 1) {
      time.value--;
    } else {
      // 倒计时结束
      clearInterval(timer.value);
      timer.value = null;
      isCounting.value = false;
      time.value = 60; // 重置为初始值
    }
  }, 1000);
};
const confrimCode = async () => {
  if (verCode.value.trim().length !== 6) {
    uni.showToast({
      title: "请输入6位验证码",
      icon: "none",
    });
    return;
  }
  const res = await request.get(`/send/checkCode`, {
    phone: phoneVal.value, // 统一使用用户输入的手机号
    businessId: userIdVal.value,
    businessType: "realName",
    code: verCode.value,
  });
  if (res.code === 20000) {
    showReal.value = true;
    codeShow.value = false;
    verCode.value = "";
    timer.value = null;
    isCounting.value = false;
    time.value = 60; // 重置为初始值
    clearInterval(timer.value);
    const { userBankInfoVO } = res.data;
    state.bankId = userBankInfoVO.cardNumber;
    state.mobile = userBankInfoVO.cardPhone;
    state.name = userBankInfoVO.idName;
    state.idCard = userBankInfoVO.idNumber;
    state.disabled = true;
    agree.value = true;
    state.isautonym = true;
    uni.setStorageSync("autonym", userBankInfoVO);
  }
};
const verCode = ref("");
const state = reactive({
  bankId: "",
  name: "",
  idCard: "",
  mobile: "",
  username: "",
  disabled: false,
  isautonym: false,
});
let userData = {};
onBeforeMount(async () => {
  userData = await Tool.getUserInfo();
  await init();
});
watch(
  () => state,
  (value) => {
    if (value.mobile == "" || value.idCard == "" || value.name == "" || value.bankId == "") {
    } else {
      state.disabled = true;
    }
  },
  { deep: true }
);
// 新增：重新发送验证码
// 修改resendCode方法
const resendCode = async () => {
  if (isCounting.value) return;

  // 清除旧定时器
  if (timer.value) {
    clearInterval(timer.value);
    timer.value = null;
  }

  // 重置倒计时状态
  isCounting.value = false;
  time.value = 60;

  await onceVerify();
};

onUnmounted(() => {
  if (timer.value) {
    clearInterval(timer.value);
    timer.value = null;
  }
});

// 新增：监听验证码抽屉关闭，清除定时器
watch(
  () => codeShow.value,
  (newVal) => {
    if (!newVal) {
      // 抽屉关闭时
      if (timer.value) {
        clearInterval(timer.value);
        timer.value = null;
        isCounting.value = false;
        time.value = 60; // 重置时间
      }
    }
  }
);
// 实名认证
const handleBtnClick = async () => {
  if (!state.disabled) return;
  if (!state.idCard.match(/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/)) {
    uni.showToast({
      title: "请输入正确格式的身份证号！",
      icon: "none",
    });
    return;
  }
  if (!state.mobile.match(/^[1][3,4,5,6,7,8,9][0-9]{9}$/)) {
    uni.showToast({
      title: "请输入正确格式的手机号！",
      icon: "none",
    });
    return;
  }
  if (!agree.value) {
    uni.showToast({
      title: "请勾选服务条款",
      icon: "none",
    });
    return;
  }

  //生成用户名
  let a = Math.random() * 10;
  if (a < 3) {
    a = +3;
  }
  const { userId } = userData.userInfo;
  const str = `0.${parseInt(a)}${userId}`;
  state.username = (str * 1).toString(36).split(".")[1].slice(0, 5);
  try {
    let params = {
      bindArgNo: "", // 参照老系统字段
      cardNumber: state.bankId.split(/[' ']/g).join(""), // 银行卡号
      cardPhone: state.mobile, // 银行预留手机号
      phone: state.mobile, // 手机号
      idName: state.name, // 姓名
      idNumber: state.idCard, // 身份证
      userId: userId, // 用户id
      username: state.username, // 用户名
    };
    console.log("params", params);
    uni.showLoading({
      title: "易旅宝",
      mask: true,
    });
    // 实名认证检验
    const { code, data } = await request.post(`/orgStructure/userBankInfo`, params);
    if (+sessionStorage.getItem("activeTab") >= 0) {
      const activeTab = +sessionStorage.getItem("activeTab");
      const item = JSON.parse(sessionStorage.getItem("traveItem"));
      if (activeTab == 0) {
        Tool.goPage.replace(`/pages/scenic/scenic?scenicId=${item.scenicId}`);
      } else if (activeTab == 1) {
        Tool.goPage.replace(`/pages/scenic/scenic?storeGoodsId=${item.storeGoodsId}`);
      } else if (activeTab == 2) {
        Tool.goPage.replace(`/pages/travelCardDetail/travelCardDetail?rightId=${item.rightsId}&storeGoodsId=${item.storeGoodsId}`);
      }
    } else {
      await init();
      uni.showToast({
        title: "实名认证成功",
        icon: "none",
      });
      // Tool.goPage.back();
    }
  } catch (err) {
    console.log(err);
  }
  uni.hideLoading();
};
//初始化
const init = async () => {
  try {
    uni.showLoading({
      title: "易旅宝",
      mask: true,
    });
    const { userId } = userData.userInfo;
    userIdVal.value = userData.userInfo.userId;
    const res = await request.get(`/orgStructure/realNameInfo/${userId}`);
    if (res.code === 20000) {
      phoneVal.value = res.data.cardPhone;
    }
    const { code, data } = await request.get(`/orgStructure/mask/realNameInfo/${userId}`);
    if (data.cardNumber == "" && data.idNumber == "") {
      // 未实名
    } else {
      hideInfoData.value = data;
      // 已实名
      state.bankId = data.cardNumber;
      state.mobile = data.cardPhone;
      state.name = data.idName;
      state.idCard = data.idNumber;
      state.disabled = true;
      agree.value = true;
      state.isautonym = true;
      uni.setStorageSync("autonym", data);
    }
  } catch (err) {
    console.log(err);
  }
  uni.hideLoading();
};
</script>

<style lang="scss" scoped>
.certification {
  height: 100%;
  background-color: #f6f6f6;
  overflow: hidden;
  > .info-box {
    padding-left: 40rpx;
    background: #ffffff;
    border-radius: 24rpx;
    > .item {
      display: flex;
      // align-items: center;
      padding: 30rpx 0;
      &.bottom-line {
        border-bottom: 1rpx solid rgba(177, 177, 177, 0.39);
      }
      .key {
        width: 130rpx;
        margin-right: 40rpx;
        font-family: none;
      }
    }

    .disabled {
      color: #f6f6f6 !important;
    }
    input {
      flex: 1;
    }
    .but {
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 20rpx;
      height: 96rpx;
      font-size: 36rpx;
      font-weight: 500;
      color: #ffffff;
      background: #7ed3da;
      border-radius: 25rpx;
      margin-top: 187rpx;
    }
  }
  .agree {
    margin-top: 30rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    .tip {
      margin-left: 4rpx;
      font-size: 26rpx;
      font-weight: 400;
      color: #050505;
      .contract {
        color: #7ed3da;
      }
    }
  }
  .infoBox {
    text-align: center;
    padding: 30rpx 0;
    .info_txtA {
      font-size: 34rpx;
      font-weight: 700;
    }
    .info_txtB {
      color: #999;
      margin-top: 18rpx;
    }
  }
  .close {
    padding: 10rpx;
  }
  .codeBox {
    padding: 40rpx 50rpx;
    .aa {
      font-size: 34rpx;
      font-weight: 700;
    }
    .bb {
      color: #999;
      margin-top: 20rpx;
    }
    .flexCode {
      display: flex;
      align-items: center;
      .code_ipt {
        margin: 30rpx 0;
        font-size: 40rpx;
      }
      .btn {
        font-size: 28rpx;
      }
    }
  }
}
</style>
