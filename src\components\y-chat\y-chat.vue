<style lang="scss" scoped>
</style>
<template>
  <div>
    <!-- TUTU -->
    <div @click="goToAiPage">
      <slot>
          <tutu />
      </slot>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue"
import tutu from "./components/tutu.vue"
import { Tool } from "../../utils/tools"

// 定义 props，sceincName 为可选参数
const props = defineProps({
  sceincName: {
    type: String,
    default: ''
  },
  scenicId: {
    type: String,
    default: ''
  }
})

// 跳转到 AI 页面
const goToAiPage = () => {
  // 如果有 sceincName 则携带参数，否则正常跳转
  if (props.sceincName) {
    Tool.goPage.push(`/pages/ai/ai?sceincName=${props.sceincName}&scenicId=${props.scenicId}`);
  } else {
    Tool.goPage.push('/pages/ai/ai');
  }
};
</script>
