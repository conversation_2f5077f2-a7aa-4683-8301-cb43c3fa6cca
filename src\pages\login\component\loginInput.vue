<template>
  <view class="input-box">
    <view class="input" v-if="type === 'mobile'">
      <y-svg name="login-phone" class="icon" />
      <input
        type="number"
        maxlength="11"
        :value="modelValue"
        @input="$emit('update:modelValue', $event.detail.value)"
        :placeholder-style="placeholderColor"
        placeholder="输入手机号"
      />
    </view>
    <view class="input" v-if="type === 'account'">
      <image class="icon" src="@/static/image/icon_accountinput.png" mode="widthFix"></image>
      <input type="text" :value="modelValue" @input="$emit('update:modelValue', $event.detail.value)" :placeholder-style="placeholderColor" placeholder="输入账号" />
    </view>
    <view class="input" v-if="type === 'password'">
      <y-svg name="login-password" class="icon" />
      <input
        :password="pwdInput === 'password'"
        :value="modelValue"
        @input="$emit('update:modelValue', $event.detail.value)"
        :placeholder-style="placeholderColor"
        placeholder="输入密码"
      />
      <view class="eye" @tap="showPwd">
        <image v-show="pwdInput === 'password'" class="icon-eye" src="@/static/image/icon_inputeye.png" mode="widthFix"></image>
        <image v-show="pwdInput === 'text'" class="icon-open-eye" src="@/static/image/icon_inputopeneye.png" mode="widthFix"></image>
      </view>
    </view>
    <view class="input" v-if="type === 'verificationCode'">
      <input type="number" :value="modelValue" maxlength="4" @input="$emit('update:modelValue', $event.detail.value)" />
      <view v-show="!isVerCount && disable" class="text-color-grey">{{ verText }}</view>
      <view v-show="!isVerCount && !disable" class="text-color-assist" @tap="getVerCode">{{ verText }}</view>
      <view v-show="isVerCount" class="text-color-assist">倒计时 {{ verCount }} s</view>
    </view>
    <view class="error-tip" v-if="errorText">
      {{ errorText }}
    </view>
    <view class="error-tip-empty" v-else></view>
  </view>
</template>

<script>
import { RegMobile } from "@/utils/regEx.js";
import request from "@/utils/request.js";
export default {
  data() {
    return {
      isVerCount: false, //是否开启验证倒计时
      verCount: 0,
      verText: "获取验证码",
      errorText: "",
      pwdInput: "password",
    };
  },
  props: {
    type: {
      type: String,
      default: "",
    },
    disable: {
      type: Boolean,
      default: true,
    },
    modelValue: String,
    mobile: String,
  },
  computed: {
    placeholderColor() {
      return { color: "#9397A8" };
    },
    linkSuffix() {
      return "";
    },
  },
  watch: {
    modelValue(newVal, oldVal) {
      this.errorText = "";
      switch (this.type) {
        case "mobile":
          const isMobile = RegMobile.test(newVal);
          this.$emit("verify", isMobile);
          if (!isMobile) {
            this.errorText = "手机号格式不正确";
          }
          break;
      }
    },
  },
  methods: {
    //获取验证码
    getVerCode() {
      const params = {
        credential: this.mobile,
        type: 2, // 1 邮箱 2 手机号
      };
      uni.showLoading({
        title: "发送验证码",
      });
      request.casPost("/otp", params).then((res) => {
        uni.hideLoading();
        uni.showToast({
          title: "验证码发送成功",
        });
        this.isVerCount = true;
        this.verCount = 60;
        const timer = setInterval(() => {
          this.verCount--;
          if (this.verCount <= 0) {
            this.isVerCount = false;
            this.verText = "重新获取";
            clearInterval(timer);
          }
        }, 1000);
      });
    },
    //显示密码
    showPwd() {
      this.pwdInput = this.pwdInput === "password" ? "text" : "password";
    },
  },
};
</script>

<style lang="scss" scoped>
.input-box {
  position: relative;
  padding: 0rpx 45rpx 0rpx 45rpx;
  .input {
    display: flex;
    align-items: center;
    height: 96rpx;
    border-radius: 12rpx;
    padding-left: 30rpx;
    padding-right: 30rpx;
    border: 1px solid #c5c5c5;
    [data-theme="blue"] & {
      background-color: rgba(102, 150, 255, 0.08) !important;
    }
    .icon {
      width: 42rpx;
      height: 42rpx;
      margin-right: 10rpx;
    }
    .eye {
      width: 60rpx;
      text-align: right;
      .icon-eye {
        width: 30rpx;
        height: 15rpx;
      }
      .icon-open-eye {
        width: 30rpx;
        height: 23rpx;
      }
    }
    input {
      flex: 1;
    }
  }
  .error-tip {
    margin-top: 10rpx;
    margin-bottom: 20rpx;
    // position: absolute;
    font-size: 24rpx;
    // line-height: 50rpx;
    color: #d86666;
  }
  .error-tip-empty {
    height: 30rpx;
  }
}
</style>
