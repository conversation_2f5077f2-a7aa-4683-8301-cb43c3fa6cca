<template>
	<uni-popup ref="popup" type="center" :is-mask-click="true" @maskClick="closePopup">
		<view class="popup-container">
			<view class="close-wrapper" @click="closePopup">
				<image class="close-icon" src="@/static/svg/close-icon.svg"></image>
			</view>
			<view class="popup-content">
				<view class="share-card-container" id="poster-container">
					<view class="share-card">
						<view class="header">
							<image class="header-icon" src="@/static/image/check-in/share-title-side.webp" />
							<text class="header-title">我的旅行打卡</text>
							<image class="header-icon" src="@/static/image/check-in/share-title-side.webp" />
						</view>

						<view class="poster-body">
							<y-base64-img v-if="shareData.image" :src="shareData.image" class="poster-image" mode="aspectFill"
								@base64-success="handlePosterBase64Success" @base64-error="handlePosterBase64Error" />
							<view class="poster-details">
								<view class="check-in-summary">
									<text>我</text>&nbsp;
									<text class="highlight">{{ shareData.checkInData.createYear }}</text>&nbsp;
									<text>年打卡的第</text>&nbsp;
									<text class="highlight">{{ shareData.checkInData.number }}</text>
									<text>&nbsp;个
										<text v-if="shareData.checkInData.city">{{ shareData.checkInData.city
										}}的</text>地点</text>
								</view>
								<view class="location-info" v-if="shareData.checkInData.address">
									<view class="location-name-group">
										<image class="location-icon" src="@/static/image/check-in-icon.webp" />
										<text class="location-name">{{ shareData.checkInData.address }}</text>
									</view>
									<text v-if="shareData.checkInData.score" class="location-rating">{{
										shareData.checkInData.score }} 分</text>
								</view>
							</view>
						</view>

						<view class="user-info">
							<view class="user-info-content">
								<y-base64-img :src="shareData.userInfo.avatar" :img-host="imgHost" class="user-avatar" mode="aspectFill"
									@base64-success="handleAvatarBase64Success" @base64-error="handleAvatarBase64Error" />
								<view class="user-details">
									<text class="user-name">{{ shareData.userInfo.nickname }}</text>
									<text class="check-in-date">{{ shareData.checkInData.createTime }}</text>
								</view>
							</view>
						</view>

						<view class="footer">
							<image class="footer-logo" src="@/static/yilvbao_logo.png" />
							<text class="qr-prompt">
								长按识别二维码
								<br />
								开启你的旅程
							</text>
							<qrcode-vue class="qr-code" :value="url" :size="50" level="M" />
						</view>
					</view>
				</view>
				<view :class="['save-button-wrapper', { 'disabled': !canSave }]" @click="handleSaveImage">
					<text class="save-button-text">{{ saveButtonText }}</text>
				</view>
			</view>
		</view>
	</uni-popup>
</template>

<script setup>
import { ref, onMounted, computed, nextTick, watch } from 'vue';
import html2canvas from 'html2canvas';
import QrcodeVue from 'qrcode.vue';
import YBase64Img from '@/components/y-base64-img/y-base64-img.vue';
import { Tool } from '@/utils/tools.ts';
import { getEnv } from "@/utils/getEnv";

const imgHost = ref(getEnv().VITE_IMG_HOST)
// 定义 props
const props = defineProps({
	shareData: {
		type: Object,
		default: () => ({
			title: '我在这里打卡成功了！',
			description: '快来看看我的打卡记录吧~',
			image: '',
			url: '',
			checkInData: {},
			userInfo: {},
		})
	}
});

// 定义 emits
const emit = defineEmits(['share', 'close']);

// 弹窗引用
const popup = ref(null);

// 弹窗开关状态
const isPopupOpen = ref(false);

// 图片转换状态
const avatarConverted = ref(false);
const posterConverted = ref(false);
// 图片 DOM 替换状态
const imageReplaced = ref(false);

// 计算属性：所有图片是否准备好
const allImagesReady = computed(() => {
	// 如果没有图片，则认为已转换
	const avatarStatus = props.shareData.userInfo.avatar ? avatarConverted.value : true;
	const posterStatus = props.shareData.image ? posterConverted.value : true;

	// 只有在图片转换完成后才能进行 DOM 替换
	return avatarStatus && posterStatus;
});

// 计算属性：是否可以保存
const canSave = computed(() => {
	// 只有在图片转换完成且 DOM 替换完成后才能保存
	return allImagesReady.value && imageReplaced.value;
});

// 保存按钮文本
const saveButtonText = computed(() => {
	if (!allImagesReady.value) {
		uni.showLoading({ title: '初始化中' });
		return '初始化中';
	}
	return imageReplaced.value ? '长按图片进行保存' : '保存图片';
});

// 处理头像转 base64 成功
const handleAvatarBase64Success = (data) => {
	console.log('头像转换成功', data);
	avatarConverted.value = true;
};

// 处理头像转 base64 失败
const handleAvatarBase64Error = (data) => {
	console.log('头像转换失败', data);
	// 即使失败也设为 true，避免用户无法保存
	avatarConverted.value = true;
};

// 处理海报图片转 base64 成功
const handlePosterBase64Success = (data) => {
	console.log('海报图片转换成功', data);
	posterConverted.value = true;
};

// 处理海报图片转 base64 失败
const handlePosterBase64Error = (data) => {
	console.log('海报图片转换失败', data);
	// 即使失败也设为 true，避免用户无法保存
	posterConverted.value = true;
};

// 自动转换图片为 DOM 元素
const convertToImageElement = async () => {
	try {
		const element = document.querySelector('#poster-container');
		if (!element) {
			console.error('找不到分享卡片元素');
			return;
		}

		// 获取原始容器的样式
		const elementStyles = window.getComputedStyle(element);

		// 使用 html2canvas 转换为图片
		const canvas = await html2canvas(element, {
			useCORS: true,
			allowTaint: true,
			backgroundColor: '#FFFFFF',
			scale: window.devicePixelRatio * 2,
		});

		// 创建图片元素
		const imgElement = document.createElement('img');
		imgElement.src = canvas.toDataURL('image/png');
		imgElement.id = 'poster-container'; // 保持原有的 ID

		// 复制原容器的所有样式
		imgElement.style.backgroundColor = elementStyles.backgroundColor;
		// imgElement.style.padding = elementStyles.padding;
		imgElement.style.borderRadius = elementStyles.borderRadius;
		imgElement.style.width = elementStyles.width;
		imgElement.style.height = elementStyles.height;
		imgElement.style.display = elementStyles.display;
		imgElement.style.boxSizing = elementStyles.boxSizing;

		// 确保图片完全填充
		imgElement.style.objectFit = 'fill';
		imgElement.style.objectPosition = 'center';

		// 替换整个 poster-container 元素
		element.parentNode.replaceChild(imgElement, element);
		imageReplaced.value = true;
		console.log('图片 DOM 替换成功');
		uni.hideLoading();
	} catch (error) {
		console.error('图片转换失败：', error);
		// 即使失败也设置为 true，避免用户无法保存
		imageReplaced.value = true;
	}
};

// 监听图片是否准备完成，完成后执行截图
watch(allImagesReady, async (ready) => {
	// 当图片准备好、弹窗已打开且 DOM 未被替换时，执行转换
	if (ready && isPopupOpen.value && !imageReplaced.value) {
		// 等待 DOM 更新
		await nextTick();
		// 添加一个小的延迟，以确保万无一失
		setTimeout(async () => {
			await convertToImageElement();
		}, 100);
	}
});

// 打开弹窗
const openPopup = async () => {
	// 重置转换状态
	avatarConverted.value = false;
	posterConverted.value = false;
	imageReplaced.value = false;
	isPopupOpen.value = true;

	if (popup.value) {
		popup.value.open();
	}
};

// 关闭弹窗
const closePopup = () => {
	isPopupOpen.value = false;
	if (popup.value) {
		popup.value.close();
	}
	emit('close');
};

const handleSaveImage = () => {
	// 如果不能保存，则直接返回
	if (!canSave.value) {
		if (!allImagesReady.value) {
			uni.showToast({
				title: '图片初始化中，请稍候...',
				icon: 'none'
			});
		}
		return;
	}

	// H5 环境
	// #ifdef H5
	// 如果图片已经替换为 DOM，提示用户长按保存
	if (imageReplaced.value) {
		uni.showToast({
			title: '请长按图片进行保存',
			icon: 'none',
			duration: 2000
		});
		return;
	}

	const element = document.querySelector('#poster-container');
	if (!element) {
		uni.showToast({ title: '找不到分享卡片元素', icon: 'none' });
		return;
	}

	uni.showLoading({ title: '正在生成图片...' });

	html2canvas(element, {
		useCORS: true, // 允许跨域加载图片
		allowTaint: true,
		backgroundColor: '#FFFFFF', // 设置背景色，避免透明
		scale: window.devicePixelRatio * 2, // 提高清晰度
	}).then(canvas => {
		// 在 H5 中，我们可以直接创建一个链接来下载
		const link = document.createElement('a');
		link.download = '我的旅行打卡.png';
		link.href = canvas.toDataURL('image/png');
		document.body.appendChild(link);
		link.click();
		document.body.removeChild(link);
		uni.hideLoading();
		uni.showToast({ title: '图片已保存', icon: 'success' });
	}).catch(err => {
		uni.hideLoading();
		uni.showToast({ title: '图片生成失败', icon: 'none' });
		console.error('html2canvas error:', err);
	});
	// #endif

	// #ifndef H5
	uni.showToast({
		title: '当前环境不支持此功能，请在 H5 环境下体验',
		icon: 'none'
	});
	// #endif
}

const url = ref('');

onMounted(() => {
	// 获取 # 号前的链接部分
	const href = window.location.href.split('#')[0];
	const storeId = Tool.getRoute.params().storeId
	url.value = `${href}#/?storeId=${storeId}`;
	console.log('获取二维码链接 str')
	console.log(url.value)
});


// 暴露方法给父组件
defineExpose({
	open: openPopup,
	close: closePopup
});
</script>

<style lang="scss" scoped>
.popup-container {
	position: relative;
}

.close-wrapper {
	position: absolute;
	top: -80rpx;
	right: 0;
	display: flex;
	align-items: center;
	padding: 10rpx;
	z-index: 10;

	.close-icon {
		width: 48rpx;
		height: 48rpx;
	}
}

.popup-content {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.share-card-container {
	background-color: #fff;
	padding: 34rpx 42rpx 68rpx 42rpx;
}

.share-card {
	display: flex;
	flex-direction: column;
	align-items: center;

	.header {
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
		width: 350rpx;

		.header-icon {
			margin: 0 8rpx -7rpx;
			width: 32rpx;
			height: 28rpx;
		}

		.header-title {
			color: #333;
			font-size: 28rpx;
			font-family: PingFangSC-Medium, sans-serif;
			font-weight: 500;
			white-space: nowrap;
		}
	}

	.poster-body {
		width: 542rpx;
		height: 704rpx;
		border-radius: 8rpx;
		display: flex;
		flex-direction: column;
		justify-content: flex-end;
		margin-top: 32rpx;
		background-color: #eee;
		position: relative;
		overflow: hidden;

		.poster-image {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			object-fit: cover;
		}

		.poster-details {
			background: linear-gradient(180deg, rgba(237, 237, 237, 0) 0%, rgba(241, 242, 245, 0.8) 27%, #F6F7FD 100%);
			background-size: 100% 100%;
			padding: 66rpx 30rpx 24rpx;
			display: flex;
			flex-direction: column;
			align-items: flex-start;
			margin-bottom: -1px;
			margin-right: -1px;
			position: relative;
			z-index: 9;

			.check-in-summary {
				font-size: 24rpx;
				font-family: PingFangSC-Medium;
				font-weight: 500;
				color: #17181a;
				line-height: 40rpx;

				.highlight {
					font-size: 44rpx;
					font-family: Helvetica-Bold;
					font-weight: 700;
					color: #349fff;
				}
			}

			.location-info {
				display: flex;
				justify-content: space-between;
				align-items: center;
				gap: 20rpx;
				margin-top: 16rpx;
				width: 100%;

				.location-name-group {
					display: flex;
					align-items: center;
					gap: 8rpx;
					min-width: 0;

					.location-icon {
						width: 42rpx;
						height: 42rpx;
						flex: none;
					}

					.location-name {
						font-size: 26rpx;
						font-weight: 500;
						color: #17181a;
						width: 100%;
						overflow: hidden;
						white-space: nowrap;
						text-overflow: ellipsis;
					}
				}

				.location-rating {
					font-size: 24rpx;
					font-weight: 500;
					color: #17181a;
					flex: none;
				}
			}
		}
	}

	.user-info {
		width: 100%;
		border-radius: 0 0 8rpx 8rpx;
		border: 2rpx solid #f3f3f3;
		padding: 34rpx 22rpx 26rpx 22rpx;
		box-sizing: border-box;

		.user-info-content {
			display: flex;
			flex-direction: row;
			align-items: center;

			.user-avatar {
				width: 72rpx;
				height: 72rpx;
				margin-right: 20rpx;
			}

			.user-details {
				display: flex;
				flex-direction: column;

				.user-name {
					color: #14131f;
					font-size: 28rpx;
					font-family: PingFangSC-Medium, sans-serif;
					font-weight: 500;
				}

				.check-in-date {
					color: #666;
					font-size: 25rpx;
					margin-top: 4rpx;
				}
			}
		}
	}

	.footer {
		display: flex;
		flex-direction: row;
		align-items: center;
		margin-top: 40rpx;
		width: 100%;

		.footer-logo {
			width: 200rpx;
			height: 60rpx;
			margin-right: auto;
		}

		.qr-prompt {
			color: #666;
			font-size: 20rpx;
			text-align: right;
			line-height: 1.4;
			margin-right: 20rpx;
		}

		.qr-code {
			width: 88rpx;
			height: 88rpx;
		}
	}
}

.save-button-wrapper {
	margin-top: 40rpx;
	background-color: #349fff;
	border-radius: 40rpx;
	padding: 20rpx 93rpx;

	&.disabled {
		background-color: #ccc;
		opacity: 0.7;
	}
}

.save-button-text {
	color: #fff;
	font-size: 28rpx;
	font-family: PingFangSC-Medium,
		sans-serif;
	font-weight: 500;
	white-space: nowrap;
	line-height: 40rpx;
}
</style>
