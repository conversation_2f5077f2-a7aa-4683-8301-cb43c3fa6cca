<template>
	<view
		class="ticket"
		:class="{ 'ticket-expired': ticket.travelGoodsStatus === 3 }">
		<view class="img">
			<image
				class="image"
				mode="aspectFill"
				v-if="ticket.picUrl"
				:src="ticket.picUrl"></image>
			<view class="image" :style="{ backgroundColor: getRandomColor() }"></view>
			<view class="boughten" v-if="ticket.travelGoodsStatus === 1">
				已购买
			</view>
			<view
				class="valid-date"
				v-if="
					ticket.travelGoodsStatus != 0 &&
					ticket.validityBeginTime &&
					ticket.validityEndTime
				">
				有效日期：{{ dayjs(ticket.validityBeginTime).format("YYYY.MM.DD") }} -
				{{ dayjs(ticket.validityEndTime).format("YYYY.MM.DD") }}
			</view>
		</view>
		<view class="content">
			<view class="left">
				<view class="title">
					{{ ticket.goodsName }}
				</view>
				<view class="sub" @click.stop="getGoodsNote(ticket.travelGoodsId)">
					权益说明
					<image
						class="arrow-right"
						src="../../static/image/order/fanhui-icon.png"
						mode="widthFix"></image>
				</view>
			</view>
			<view class="right">
				<text class="unit">￥</text>
				<text class="num">{{ ticket.salePrice }}</text>
			</view>
		</view>
		<image
			class="expired-icon"
			src="../../static/image/ticket-expired.png"
			mode=""></image>
		<y-popup v-model="isPopUpWin" title="权益说明">
			<view v-if="notice" class="rich-content" style="padding-left:46rpx;padding-right:46rpx;">
				<rich-text :nodes="notice"></rich-text>
			</view>
			<y-empty v-else>暂无内容</y-empty>
		</y-popup>
	</view>
</template>

<script setup>
import { ref, toRefs } from "vue"
import { getRandomColor } from "@/utils/tool.js"
import request from "@/utils/request.js"
import dayjs from "dayjs"
import { marked } from 'marked' 

const props = defineProps({
	ticket: {
		type: Object,
		default: () => ({}),
		required: true
	}
})
const isPopUpWin = ref(false)

const notice = ref("")
// 获取须知信息
const getGoodsNote = async travelGoodsId => {
	uni.showLoading({
		title: "易旅宝",
		mask: true
	})
	const { data } = await request.get(`/travelGoods/info/${travelGoodsId}`)
	uni.hideLoading()
	// notice.value = data.notices ? data.notices : ""
		if (data.notices) {
    // 更健壮的 Markdown 转换
    try {
      // 预处理：移除可能干扰的字符
      const cleanContent = data.notices
        .replace(/\\\*/g, '') // 移除转义星号
        .replace(/(\*\*)([^*]+)(\*\*)/g, '**$2**'); // 标准化加粗语法
      
      // 转换 Markdown
      notice.value = marked.parse(cleanContent, {
        gfm: true,
        breaks: true,
        sanitize: true
      });
    } catch (e) {
      console.error('Markdown 解析错误：', e);
      notice.value = data.notices; // 回退到原始内容
    }
  } else {
    notice.value = ""
  }
	isPopUpWin.value = true
}
</script>

<style lang="scss" scoped>
.ticket {
	position: relative;
	--radius-size: 28rpx;
	margin: 30rpx 0;
	width: 100%;
	overflow: hidden;
	height: 466rpx;
	border-radius: var(--radius-size);
	background-color: #fff;
	display: flex;
	flex-direction: column;
	box-shadow: 0rpx 2rpx 9rpx 3rpx rgba(195, 206, 218, 0.31);
	> .img {
		// padding: 12rpx;
		// background: url('https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fimg.jj20.com%2Fup%2Fallimg%2F1114%2F0QR0103525%2F200QQ03525-1-1200.jpg&refer=http%3A%2F%2Fimg.jj20.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1655609440&t=d1c0084aab0e4e66f1cc3864af2fb8a6') no-repeat;
		// background-size: cover;
		position: relative;
		width: 100%;
		height: 340rpx;
		border-radius: var(--radius-size) var(--radius-size) 0 0;
		overflow: hidden;
		.image {
			width: 100%;
			height: 100%;
		}
		.boughten {
			width: 124rpx;
			height: 50rpx;
			position: absolute;
			top: 0;
			right: 0;
			background: linear-gradient(90deg, #f9dab0 0%, #eb9c51 100%);
			border-radius: 0rpx 30rpx 0rpx 30rpx;
			font-size: 22rpx;
			color: #541f0d;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 500;
		}
		.valid-date {
			color: #fff;
			position: absolute;
			bottom: 0;
			left: 0;
			right: 0;
			padding-left: 30rpx;
			padding-top: 60rpx;
			padding-bottom: 10rpx;
			font-size: 26rpx;
			background: linear-gradient(
				to top,
				rgba(0, 0, 0, 0.15),
				rgba(255, 0, 0, 0)
			);
		}
	}
	.content {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0 30rpx;
		height: 125rpx;
		.left {
			.title {
				margin-bottom: 10rpx;
				font-size: 34rpx;
				font-weight: 500;
				color: #050505;
				line-height: 34rpx;
			}
			.sub {
				display: flex;
				align-items: center;
				font-size: 26rpx;
				font-weight: 400;
				color: #1c78e9;
				image {
					margin-left: 8rpx;
					width: 14rpx;
				}
			}
		}
		.right {
			font-size: 26rpx;
			font-weight: 400;
			color: #6b6b6b;
			.unit {
				font-size: 28rpx;
				color: #f43636;
			}
			.num {
				font-size: 42rpx;
				color: #f43636;
			}
		}
	}
	.expired-icon {
		display: none;
		width: 185rpx;
		height: 185rpx;
		position: absolute;
		bottom: 53rpx;
		right: 30rpx;
	}
}
.ticket-expired {
	.sub,
	.unit,
	.num {
		color: #9c9c9c !important;
	}
	.arrow-right {
		filter: grayscale(100%);
	}
	.expired-icon {
		display: block;
	}
}
</style>
