<template>
	<view :class="['tab', tabList[activeIndex].color]" :style="style">
		<view class="bar">
			<view v-for="(item, index) in tabList" :key="index" class="list">
				<view
					:class="['title', activeIndex === index ? item.color : '']"
					@click="handleTabClick(item, index)"
					>{{ item.label }}</view
				>
				<!-- <view class="divider" v-if="index !== tabList.length - 1"></view> -->
			</view>
		</view>
		<view class="content">
			<slot></slot>
		</view>
	</view>
</template>
<script lang="ts" setup>
interface TabControlProps {
	tabList: {
		label: string
		key: string
	}[]
	onChange?: (key: string) => void
	defaultKey?: string
	style?: CSSProperties
}
import { ref, type CSSProperties } from "vue"
import { onShow, onReachBottom } from "@dcloudio/uni-app"
const { onChange, style = {} } = defineProps<TabControlProps>()

const tabList = ref([
	{
		label: "景区",
		key: "scenic",
		color: "blue"
	},
	{
		label: "组合套票",
		key: "combination",
		color: "orange"
	}
])
const activeIndex = ref(0)

const handleTabClick = (item, index) => {
	activeIndex.value = index
	onChange && onChange(item, index)
}
onShow(() => {
	activeIndex.value = 0
	handleTabClick(tabList.value[0], 0)
})
</script>

<style lang="scss" scoped>
@mixin tab-suffix($color) {
	position: relative;
	font-size: 34rpx;
	line-height: 42rpx;
}

.tab {
	border-radius: 12rpx;
	display: flex;
	flex-direction: column;
	padding: 0 10rpx 20rpx;
	.bar {
		display: flex;
		padding: 26rpx 0;
		justify-content: space-around;
		.list {
			display: flex;
			justify-content: center;
			flex: 1;
			&:not(:last-child) {
				border-right: 2rpx solid rgba(255, 255, 255, 0.43);
			}
		}
		.title {
			font-size: 30rpx;
			font-weight: 500;
			line-height: 40rpx;
			transition: 0.3s;
			position: relative;
		}

		.blue {
			color: #003b84;
			background: transparent;
			@include tab-suffix(#003b84);
		}
		.orange {
			color: #e64016;
			background: transparent;
			@include tab-suffix(#e64016);
		}
		.green {
			color: #149aad;
			background: transparent;
			@include tab-suffix(#149aad);
		}
	}
	.content {
		flex: 1;
	}
}

.blue {
	background: linear-gradient(
		180deg,
		#93c3ff 0%,
		rgba(238, 244, 251, 0.54) 34%,
		rgba(217, 242, 255, 0) 100%
	);
}

.orange {
	background: linear-gradient(180deg, #ffc4c1 0%, rgba(255, 255, 255, 0) 100%);
}

.green {
	background: linear-gradient(
		180deg,
		#d5fff5 0%,
		rgba(238, 250, 251, 0.63) 58%,
		#f2fffd 100%
	);
}
</style>
