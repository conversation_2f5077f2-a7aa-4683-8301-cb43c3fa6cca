<template>
	<view class="ticket-info panel-box">
		<view class="ticket-title">
			<text>
				<template v-for="(item, index) in order.ticketName" :key="index">
					<y-font-weight>{{ item }}</y-font-weight>
				</template>
			</text>
			<text style="font-size: 26rpx; white-space: nowrap">&nbsp;x{{ order.ticketNumber }}份</text>
		</view>
		<view class="ticket__item" v-if="order.enterTime">
			<text class="ticket__item-title">入园时间</text>
			{{ order.enterTime }}
		</view>
		<view class="ticket__item" v-if="order.ticketInfo.scenicInfoVo">
			<text class="ticket__item-title">入园地址</text>
			<view class="ticket__item-address">{{
				order.ticketInfo.scenicInfoVo.address
			}}</view>
		</view>
		<view class="ticket__item" v-if="order.ticketInfo.scenicId">
			<text class="ticket__item-title"></text>
			<ScenicAddress :scenic-info="{
				...order.ticketInfo.scenicInfoVo,
				scenicId: order.ticketInfo.scenicId
			}" />
		</view>
		<view class="ticket__item" v-if="order.ticketInfo.scenicId">
			<text class="ticket__item-title">退改规则</text>
			<view @tap="popModel" style="color: var(--theme-color)">查看详情</view>
		</view>
		<view class="ticket__item">
			<text class="ticket__item-title">类型</text>
			{{ order.orderType }}
		</view>

		<!-- 权益卡 -->
		<view class="ticket-list" v-if="order.isTravelCard">
			<view class="ticket-list-title">
				<y-font-weight>游客信息</y-font-weight>
				<view class="eye" @tap="switchIdentity">
					<image v-show="!showIdentity" class="icon-eye" src="@/static/image/icon_inputeye.png"
						mode="widthFix"></image>
					<image v-show="showIdentity" class="icon-open-eye" src="@/static/image/icon_inputopeneye.png"
						mode="widthFix"></image>
				</view>
			</view>
			<view class="ticket-item">
				<div>
					<view class="ticket-realname">
						<view class="realname-left"> 姓&emsp;名 </view>
						<view class="realname-right">{{
							order.travelCardInfo.identityName || "--"
						}}</view>
					</view>
					<view class="ticket-realname">
						<view class="realname-left"> 身份证 </view>
						<view class="realname-right" v-if="order.travelCardInfo.identity && !showIdentity">{{
							`${order.travelCardInfo.identity.slice(0, 3)}***********${order.travelCardInfo.identity.slice(
								-4
							)}`
						}}</view>
						<view class="realname-right" v-else-if="order.travelCardInfo.identity && showIdentity">{{
							`${order.travelCardInfo.identity}`
						}}</view>
						<view class="realname-right" v-else>--</view>
					</view>
				</div>
			</view>
		</view>
		<!-- 权益票 / 一票一人 -->
		<view v-if="
			order.isSingle &&
			!order.isSingleMorePeople &&
			order.touristInfo.length > 0
		">
			<!-- 实名制 -->
			<view class="ticket-list" v-if="order.ticketInfo.realName">
				<view class="ticket-list-title">
					<y-font-weight>游客信息</y-font-weight>
					<view class="eye" @tap="switchIdentity">
						<image v-show="!showIdentity" class="icon-eye" src="@/static/image/icon_inputeye.png"
							mode="widthFix"></image>
						<image v-show="showIdentity" class="icon-open-eye" src="@/static/image/icon_inputopeneye.png"
							mode="widthFix"></image>
					</view>
				</view>
				<view class="ticket-item" v-for="(item, index) in order.touristInfo"
					@click="goTicketDetail(item.orderId, item.ticketNumber)" :key="index">
					<view class="check" v-if="refundTicketList.length > 0">
						<y-checkbox :checked="refundTicketList.includes(item.ticketNumber)"
							@onCheck="onCheck(item.ticketNumber)"></y-checkbox>
					</view>
					<view v-if="!isRefundOrder || item.disabled">
						<template v-if="item.realNameList.length > 0">
							<view class="ticket-realname">
								<view class="realname-left"> 姓名 </view>
								<view class="realname-right">{{
									item.realNameList[0].name
								}}</view>
							</view>
							<view class="ticket-realname">
								<view class="realname-left"> 身份证 </view>
								<view class="realname-right" v-if="item.realNameList[0].identity && !showIdentity">{{
									`${item.realNameList[0].identity.slice(0, 3)}***********${item.realNameList[0].identity.slice(
										-4
									)}`
								}}</view>
								<view class="realname-right" v-else-if="item.realNameList[0].identity && showIdentity">{{
									`${item.realNameList[0].identity}`
								}}</view>
								<view class="realname-right" v-else>--</view>
							</view>
						</template>

						<view class="ticket-realname" v-if="item.ticketStatus">
							<view class="realname-left"> 状态 </view>
							<view class="realname-right">{{
								ticketStatus[item.ticketStatus]
							}}</view>
						</view>
						<view class="ticket-img">
							<image v-if="item.showQRCode" @tap="goTicketDetail(item.orderId, item.ticketNumber)"
								class="icon" src="@/static/image/order/orderdetal-qrcode-icon.png" mode="widthFix" />
							<template v-if="item.showFace">
								<image @click.stop="
	Tool.goPage.push(
										`/pages/faceRecognition/faceRecognition?identity=${item.realNameList[0].identity}&ticketNumber=${item.ticketNumber}&name=${item.realNameList[0].name}&scenicId=${item.scenicId}`
									)
									" class="icon" src="@/static/image/order/orderdetal-face-icon.png" mode="widthFix" />
							</template>
						</view>
					</view>
				</view>
			</view>
			<!-- 非实名 -->
			<view class="ticket-list" v-else-if="order.touristInfo[activeQrIndex].printStr">
				<view class="ticket-no-realname">
					<uni-icons :style="{ opacity: order.touristInfo.length > 1 ? 1 : 0 }" type="left" size="30"
						color="#000" @click="toggleQrcode('left', order.touristInfo.length)" />
					<view>
						<y-ticket-qrcode :qr-str="order.touristInfo[activeQrIndex].printStr"
							:status="order.touristInfo[activeQrIndex].ticketStatus"
							:ticket-id="order.touristInfo[activeQrIndex].ticketNumber"
							:order-id="order.touristInfo[activeQrIndex].orderId" />
					</view>
					<uni-icons :style="{ opacity: order.touristInfo.length > 1 ? 1 : 0 }" type="right" size="30"
						color="#000" @click="toggleQrcode('right', order.touristInfo.length)" />
				</view>
				<view style="text-align: center; margin-top: 10rpx">{{ activeQrIndex + 1 }}/{{ order.touristInfo.length
					}}</view>
			</view>
		</view>
		<!-- 一票多人 -->
		<template v-if="order.isSingle && order.isSingleMorePeople">
			<view class="ticket__item" v-if="order.touristInfo[0].ticketStatus">
				<text class="ticket__item-title">状态</text>
				<text class="realname-right" v-if="order.touristInfo[0].ticketStatus">{{
					ticketStatus[order.touristInfo[0].ticketStatus]
				}}</text>
				<text class="realname-right" v-else>--</text>
			</view>
			<!-- 二维码 -->
			<view class="more-people" v-if="order.touristInfo[0].flag == 1">
				<view class="">
					<y-ticket-qrcode :qr-str="order.touristInfo[0].printStr"
						:ticketId="order.touristInfo[0].ticketNumber" :orderId="order.touristInfo[0].orderId"
						:status="order.touristInfo[0].ticketStatus" />
				</view>
			</view>
			<!-- 非二维码展示票号 -->
			<view v-else class="ticket__item">
				<text class="ticket__item-title">票&emsp;号</text>
				<view class="realname-right" v-if="order.touristInfo[0].ticketNumber">{{
					order.touristInfo[0].ticketNumber.slice(0, 10) }}...{{
						order.touristInfo[0].ticketNumber.slice(-9)
					}}</view>
				<view class="realname-right" v-else>--</view>
				<view class="text-btn" @click="
					goTicketDetail(
						order.touristInfo[0].orderId,
						order.touristInfo[0].ticketNumber
					)
					">查看门票</view>
			</view>

			<view class="ticket-list" v-if="order.touristInfo[0].realNameList.length > 0">
				<view class="ticket-list-title">
					<y-font-weight>游客信息</y-font-weight>
					<view class="eye" @tap="switchIdentity">
						<image v-show="!showIdentity" class="icon-eye" src="@/static/image/icon_inputeye.png"
							mode="widthFix"></image>
						<image v-show="showIdentity" class="icon-open-eye" src="@/static/image/icon_inputopeneye.png"
							mode="widthFix"></image>
					</view>
				</view>

				<view class="ticket-item" v-for="(item, index) in order.touristInfo[0].realNameList" :key="index">
					<view class="check" v-if="order.touristInfo[0].showRefund">
						<y-checkbox :checked="order.touristInfo[0].showRefund" :disabled="order.touristInfo[0].disabled"
							@onCheck="onCheck(item)"></y-checkbox>
					</view>
					<view style="display: flex; justify-content: space-between; width: 100%"
						v-if="isRefundOrder ? order.touristInfo[0].disabled : true">
						<div>
							<view class="ticket-realname">
								<view class="realname-left"> 姓&emsp;名 </view>
								<view class="realname-right" v-if="item.name">{{
									item.name
								}}</view>
								<view class="realname-right" v-else>--</view>
							</view>
							<view class="ticket-realname">
								<view class="realname-left"> 身份证 </view>
								<view class="realname-right" v-if="item.identity && !showIdentity">{{
									`${item.identity.slice(0, 3)}***********${item.identity.slice(
										-4
									)}`
								}}</view>
								<view class="realname-right" v-else-if="item.identity && showIdentity">{{
									`${item.identity}`
								}}</view>
								<view class="realname-right" v-else>--</view>
							</view>
						</div>
						<view class="ticket-img" v-if="order.touristInfo[0].showFace">
							<image @tap="
	Tool.goPage.push(
									`/pages/faceRecognition/faceRecognition?identity=${item.identity}&ticketNumber=${order.touristInfo[0].ticketNumber}&name=${item.name}&scenicId=${order.touristInfo[0].scenicId}`
								)
								" class="icon" src="@/static/image/order/orderdetal-face-icon.png" mode="widthFix" />
						</view>
					</view>
				</view>
			</view>
		</template>

		<!-- 退订规则 -->
		<y-popup v-model="isPopUpWin" type="reserve" title="退订规则">
			<view v-if="noteContent" v-html="noteContent" class="rich-content"></view>
			<y-empty v-else>暂无内容</y-empty>
		</y-popup>
	</view>
</template>
<script setup>
import { toRefs, reactive, ref, watch, onMounted } from "vue"
import { ticketStatus } from "@/utils/constant.js"
import ScenicAddress from "./l-scenic-address.vue"
import request from "@/utils/request.js"
import { markdownToHtml } from "@/utils/tool.js"
import { getEnv } from "@/utils/getEnv";

const props = defineProps({
	//主题
	theme: {
		type: String,
		default: "redTheme"
	},
	// 退票列表
	refundTicketList: {
		type: Array,
		default: () => []
	},
	//退票勾选展示
	showRefund: {
		type: Boolean,
		default: false
	},
	//是否退单
	isRefundOrder: {
		type: Boolean,
		default: false
	},
	order: {
		type: Object,
		default: {}
	}
})
const emits = defineEmits(["onCheck"])

const activeQrIndex = ref(0)
const showIdentity = ref(false)

const imgHost = getEnv().VITE_IMG_HOST

// 跳转票详情
const goTicketDetail = (orderId, ticketNumber) => {
	Tool.goPage.push(
		`/pages/ETicket/ETicket?ticketNumber=${ticketNumber}&orderId=${orderId}`
	)
}
// 切换二维码
const toggleQrcode = (type, length) => {
	if (type === "left") {
		activeQrIndex.value = activeQrIndex.value - 1
		if (activeQrIndex.value < 0) {
			activeQrIndex.value = length - 1
		}
	} else {
		activeQrIndex.value = activeQrIndex.value + 1
		if (activeQrIndex.value >= length) {
			activeQrIndex.value = 0
		}
	}
}

console.log('props.order=====ooooooo')
console.log(props.order)

//预定须知弹窗
const isPopUpWin = ref(false)
const noteContent = ref("")
const popModel = async () => {
	const goodsId = props.order.ticketInfo.productSkuId
	const { data } = await request.get(`/appScenic/goodsNote/${goodsId}`)
	noteContent.value = markdownToHtml(data)
	isPopUpWin.value = true
}
const switchIdentity = () => {
	showIdentity.value = !showIdentity.value;
}

onMounted(async () => {})
//获取检票规则
const getCheckRule = async id => {
	try {
		// uni.showLoading({mask:true});
		const { code, data } = await request.get(`/ticketCheck/info`, { id: id })
		return data.identityTypeList
	} catch (err) {
		console.log(err)
	}
	return null
	// uni.hideLoading();
}
const onCheck = ticketNumber => {
	const list = [...props.refundTicketList]
	if (list.includes(ticketNumber)) {
		// 取消勾选
		const index = list.findIndex(item => item === ticketNumber)
		list.splice(index, 1)
	} else {
		// 勾选
		list.push(ticketNumber)
	}
	emits("onCheck", { list })
}
</script>
<style lang="scss" scoped>
.ticket-info {
	background-color: #fff;
	padding: 30rpx;
	margin: 20rpx 0;
	border-radius: 12rpx;
	.ticket-title {
		font-size: 36rpx;
		font-weight: 500;
		color: #050505;
		margin-bottom: 23rpx;
	}
	> .ticket__item {
		display: flex;

		font-size: 28rpx;
		color: #14131f;
		&:not(:last-child) {
			margin-bottom: 20rpx;
		}
		.ticket__item-title {
			display: flex;
			justify-content: space-between;
			font-size: 28rpx;
			width: 117rpx;
			color: #6e6e6e;
			margin-right: 26rpx;
			flex: none;
		}
		.ticket__item-address {
			flex: 1;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}
		.text-btn {
			margin-left: auto;
			color: var(--theme-color);
		}
	}
	.ticket-list {
		padding: 30rpx 20rpx;
		background-color: #f3f7fd;

		border-radius: 12rpx;

		.ticket-list-title {
			font-size: 34rpx;
			font-weight: 500;
			padding-bottom: 30rpx;
			display: flex;
			justify-content: space-between; 
    		align-items: center; 
			.eye {
				width: 60rpx;
				text-align: right;
				.icon-eye {
					width: 30rpx;
					height: 15rpx;
				}
				.icon-open-eye {
					width: 30rpx;
					height: 23rpx;
				}
			}
		}
		.ticket-item {
			// padding-top: 30rpx;
			margin-bottom: -20rpx;
			display: flex;
			align-items: center;
			border-top: 2rpx solid rgb(228, 228, 228, 0.6);
			.check {
				margin-right: 20rpx;
			}
			.title {
				font-size: 30rpx;
				font-weight: 500;
				color: #050505;
			}
			.yuyue {
				margin-top: 16rpx;
				font-size: 26rpx;
				font-weight: 300;
				color: #050505;
			}
			.ticket-realname {
				display: flex;
				margin: 20rpx 0;
				align-items: center;
				font-size: 28rpx;
				width: 100%;
				.realname-left {
					color: #6e6e6e;
					width: 86rpx;
					margin-right: 24rpx;
					text-align-last: justify;
				}
				.realname-right {
					font-weight: 400;
					color: #14131f;
				}
			}
			.ticket-img {
				margin: 20rpx 0;
				display: flex;
				.icon {
					width: 80rpx;
					height: 80rpx;
					margin-right: 43rpx;
				}
			}
		}
	}
}
.more-people {
	display: flex;
	flex-direction: column;
	align-items: center;
	.icon {
		width: 80%;
	}
	.ticket-num {
		margin-top: 10rpx;
		padding-bottom: 10rpx;
		word-wrap: break-word;
		width: 100%;
		text-align: center;
	}
}
.ticket-no-realname {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

// .tourist-info[theme="redTheme"] {
// 	--color1: #d43e3e;
// 	--color2: #ffb4b4;
// 	--bg1: #fff8f8;
// }
// .tourist-info[theme="blueTheme"] {
// 	--color1: #024abb;
// 	--color2: #61b4fc;
// 	--bg1: #f8fbff;
// }
// .image {
// 	margin-top: 30rpx;
// 	width: 100rpx !important;
// }
// .icon {
// 	height: 100%;
// 	width: 100%;
// }
// .tourist-info {
// 	background-color: #fff;
// 	border-radius: 18rpx;
// 	// margin-bottom: 30rpx;
// 	margin-top: 30rpx;
// 	padding: 34rpx 30rpx 30rpx;
// 	> .title {
// 		display: flex;
// 		align-items: center;
// 		padding-bottom: 30rpx;
// 		font-size: 34rpx;
// 		font-weight: 500;
// 		color: #050505;
// 		// background-color: var(--color2);
// 		border-radius: 24px 24px 0px 0px;
// 		border-bottom: 2rpx solid rgba(228, 228, 228, 0.39);
// 	}
// 	.tourist-check {
// 		width: 100%;
// 		display: flex;
// 		flex-direction: column;
// 		// flex-direction: column;

// 		.tourist-item {
// 			display: flex;
// 			width: 100%;
// 			background-color: var(--bg1);
// 			overflow: hidden;
// 			&:not(:last-child) {
// 				border-bottom: 2rpx solid rgba(228, 228, 228, 0.39);
// 			}
// 			.user-item {
// 				display: flex;
// 				justify-content: space-between;
// 				flex: 1;
// 				margin: 33rpx 0;
// 				.ticket-realname {
// 					display: flex;
// 					align-items: center;
// 					margin-bottom: 30rpx;
// 					font-size: 28rpx;
// 					font-weight: 400;
// 					color: #040404;
// 					&:last-child {
// 						margin-bottom: 0;
// 					}
// 					.name {
// 						margin-right: 37rpx;
// 						font-family: none;
// 					}
// 				}
// 			}

// 			.group-picture {
// 				display: flex;
// 				margin-top: 41rpx;
// 				margin-bottom: 20rpx;
// 				.item {
// 					height: 80rpx;
// 					width: 80rpx;
// 					margin-right: 42rpx;
// 				}
// 			}
// 			.check {
// 				margin-right: 23rpx;
// 				display: flex;
// 				justify-content: center;
// 				align-items: center;
// 			}
// 		}
// 	}
// }
// .add-ticket {
// 	display: flex;
// 	align-items: center;
// 	justify-content: center;
// 	margin: 0 20rpx;
// 	height: 96rpx;
// 	font-size: 36rpx;
// 	font-weight: 500;
// 	color: #ffffff;
// 	background: #7ed3da;
// 	border-radius: 25rpx;
// }
</style>
