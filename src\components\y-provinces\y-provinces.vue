<template>
	<picker
		@columnchange="bindPickerChange"
		@change="changeProvinces"
		:value="activeProvinces"
		range-key="addressName"
		:range="provincesList"
		mode="multiSelector">
		<view class="privices" v-if="provincesList.length > 0">
			<template v-if="activeProvinces.length > 0">
				<template class="uni-input" v-for="(e, i) in activeProvinces" :key="i">
					&ensp;{{ provincesList[i][e].addressName }}
				</template>
			</template>
			<template v-else-if="defaultValue.length > 0">
				<template class="uni-input" v-for="(e, i) in defaultValue" :key="i">
					&ensp;{{ e }}
				</template>
			</template>
			<template v-else> 请选择地区 </template>
		</view>
	</picker>
</template>
<script setup>
import { toRefs, reactive, ref, onMounted } from "vue"
import request from "@/utils/request.js"
const props = defineProps({
	defaultValue: {
		type: Array,
		default: []
	}
})

const emits = defineEmits(["onChange"])

const activeProvinces = ref([])
const provincesList = ref([])

// 获取省市区
const bindPickerChange = async e => {
	let { column, value, isInit } = e.detail
	if (column >= 3) return
	const addressId = isInit ? "" : provincesList.value[column][value].addressId
	const params = {
		id: addressId
	}
	const { code, data } = await request.get(`/address/info`, params)
	if (isInit) {
		provincesList.value.push(data)
	} else {
		column++
		provincesList.value[column] = data
	}

	if (data && data.length > 0 && column < 3) {
		//递归查下一级
		bindPickerChange({
			detail: {
				column: column,
				value: 0
			}
		})
	}
}

//确认省市区
const changeProvinces = async e => {
	console.log("确认省市区")
	// console.log(e);
	activeProvinces.value = e.detail.value

	const current = activeProvinces.value.map((e, i) => {
		return provincesList.value[i][e]
	})
	emits("onChange", current)
}

//初始化
onMounted(() => {
	bindPickerChange({
		detail: {
			column: 0,
			value: 0,
			isInit: true
		}
	})
})
</script>
<style lang="scss" scoped>
.privices {
	display: flex;
	margin-left: 20rpx;
}
</style>
