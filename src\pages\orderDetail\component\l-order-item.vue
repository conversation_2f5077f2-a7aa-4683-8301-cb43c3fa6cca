<template>
	<view class="order-item"  @tap='toPay'>
		<view class="top">
			<view class="order-id"><text>退单号：{{order.refundId}}</text> </view>
			<view class="order-status">{{orderStatus[order.refundStatus]}}</view>
		</view>
		<view class="middle">
			<text class="order-title">
				<y-font-weight>{{order.ticketInfo[0] && order.ticketInfo[0].productSkuName}}</y-font-weight>
			</text>
			<text class="order-num">数量：{{order.ticketInfo.length}}</text>
		</view>
		<view class="bottom">
			<view class="order-price">
				<text class="unit">¥</text>{{order.payAmount || order.refundAmount}}
			</view>
			<text v-if="order.orderStatus=='20'"  class="go-pay">去支付</text>
		</view>
	</view>
</template>

<script setup >
	import { toRefs, reactive, ref } from 'vue'
import { orderStatus } from "@/utils/constant.js"
		name:"orderItem";
		const props =defineProps({
			order: {
				type: Object,
				default: {},
				required: true
			},
			orderId: {
				type: String,
				default: '',
				required: true
			}
		})
		const toPay=()=>{
			Tool.goPage.push(`/pages/orderDetail/orderDetail?orderId=${props.orderId}&refundStatus=${props.order.refundStatus}&refundId=${props.order.refundId}`);
		};
</script>

<style lang="scss" scoped>
	.order-item{
		padding: 30rpx;
		border-radius: 18rpx;
		background-color: #fff;
		box-shadow: 0px 2px 9px 3px rgba(195,206,218,0.3100);
		margin: 30rpx auto 0;
		width: 95%;
		.top{
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding-bottom: 30rpx;
			font-size: 26rpx;
			border-bottom: 2rpx solid rgba(228, 228, 228, 0.4);
			.order-id{
				display: flex;
				flex: 1;
				width: 0;
				color: #131313;
				text{
					@include text-truncate;
				}
			}
			.order-status{
				flex: none;
				margin-left: 20rpx;
				color: #050505;
			}
		}
		.middle{
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin: 25rpx 0 20rpx;
			.order-title{
				font-size: 32rpx;
				font-weight: 600;
				color: #131313;
			}
			.order-num{
				flex: none;
				align-self: flex-start;
				font-size: 26rpx;
				line-height: 45rpx;
				color: #050505;
			}
		}
		.bottom{
			display: flex;
			justify-content: space-between;
			.order-price{
				font-size: 48rpx;
				color: #F43636;
				font-weight: 500;
				.unit{
					display: inline-block;
					font-size: 28rpx;
					margin-right: 6rpx;
				}
			}
			.go-pay{
				display: flex;
				justify-content: center;
				align-items: center;
				border: 1px solid #FF9201;
				border-radius: 28rpx;
				padding: 10rpx 33rpx;
				font-size: 28rpx;
				font-weight: 500;
				color: #FF9201;
			}
		}
	}
</style>