<template>
	<view class="ticket__item-map">
		<image class="map-icon" src="@/static/image/map-icon.png" mode="widthFix" />
		<view class="map-item" @click="nav">
			<y-svg
				style="width: 40rpx; height: 40rpx; margin-top: -5rpx"
				name="navigation-icon" />
			去这里</view
		>
		<view style="width: 1rpx; height: 30rpx; background: #e3e3e3" />
		<view class="map-item" @click="callPhone">
			<y-svg style="width: 40rpx; height: 40rpx" name="phone-icon" />
		</view>
		<view style="width: 1rpx; height: 30rpx; background: #e3e3e3" />
		<view class="map-item" @click="goScenicDetail">详情</view>
	</view>
</template>
<script setup>
import { toRefs, reactive, ref, watch, onMounted } from "vue"
import { ticketStatus } from "@/utils/constant.js"
import { setJWeixin } from "@/utils/tool.js"
const props = defineProps({
	scenicInfo: {
		type: Object,
		default: {}
	}
})
setJWeixin()
// 跳转第三方导航
const nav = () => {
	const { scenicInfo } = props
	console.log({
		latitude: Number(scenicInfo?.latitude),
		longitude: Number(scenicInfo?.longitude),
		// name: props.order.ticketInfo?.scenicName,
		address: scenicInfo?.address
	})
	jWeixin.openLocation({
		latitude: Number(scenicInfo?.latitude),
		longitude: Number(scenicInfo?.longitude),
		// name: props.order.ticketInfo?.scenicName,
		address: scenicInfo?.address
	})
}

//拨打电话
const callPhone = () => {
	const { scenicInfo } = props
	if (scenicInfo?.contactPhone) {
		uni.makePhoneCall({
			phoneNumber: scenicInfo.contactPhone
		})
	}
}
// 跳转景区详情
const goScenicDetail = () => {
	const { scenicInfo } = props
	Tool.goPage.push(`/pages/scenic/scenic?scenicId=${scenicInfo.scenicId}`)
}
</script>
<style lang="scss" scoped>
.ticket__item-map {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: space-between;
	border: 2rpx solid rgba(151, 151, 151, 0.3);
	border-radius: 6rpx;
	padding: 8rpx;
	.map-icon {
		width: 106rpx;
	}
	.map-item {
		color: var(--theme-color);
		padding: 0 22rpx;
		display: flex;
		align-items: center;
	}
}
</style>
