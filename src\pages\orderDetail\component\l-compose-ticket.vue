<template>
	<view class="ticket-info panel-box">
		<view class="ticket-title">
			<template v-for="(item, index) in order.ticketName" :key="index">
				<y-font-weight>{{ item }}</y-font-weight>
			</template>
			<text style="font-size: 26rpx; white-space: nowrap">&nbsp;x{{ order.composeInfo.num }}份</text>
		</view>
		<!-- 切换票详情 -->
		<view class="equal-width-container">
			<view v-for="(item, index) in order.composeTicketList.length" :key="index" class="equal-width-item"
				:class="{ active: selectedIndex === index }" @click="selectItem(index)">票{{ index + 1 }}</view>
		</view>
		<view class="ticket-title">
			<y-font-weight>{{ ticketInfo.productSkuName }}&ensp;X{{ ticketInfo.num }}份</y-font-weight>
		</view>
		<view class="ticket__item"><text class="ticket__item-title">类型</text>{{ ticketType[ticketInfo.productType] }}
		</view>
		<view class="ticket__item" v-if="ticketInfo.day">
			<text class="ticket__item-title">入园时间</text>
			{{ ticketInfo.day }}
		</view>
		<view class="ticket__item" v-if="ticketInfo.scenicInfoVo">
			<text class="ticket__item-title">入园地址</text>
			<view class="ticket__item-address">{{
				ticketInfo.scenicInfoVo.address
			}}</view>
		</view>

		<view class="ticket__item" v-if="ticketInfo.scenicId">
			<text class="ticket__item-title"></text>
			<ScenicAddress :scenic-info="{
				...ticketInfo.scenicInfoVo,
				scenicId: ticketInfo.scenicId
			}" />
		</view>
		<view class="ticket__item">
			<text class="ticket__item-title">退改规则</text>
			<view @tap="popModel" style="color: var(--theme-color)">查看详情</view>
		</view>
		<view style="margin: 40rpx 0;" v-if="ticketInfo.QRCodes.length > 0">
			<QRCode :QRCodes="ticketInfo.QRCodes" @onQrClick="goQRCode(ticketInfo)" />
		</view>

		<view class="ticket-list" v-if="ticketInfo._realNameList.length > 0">
			<view class="ticket-title">
				<view>
					游客信息
				</view>
				<view class="eye" @tap="switchIdentity">
					<image v-show="!showIdentity" class="icon-eye" src="@/static/image/icon_inputeye.png" mode="widthFix"></image>
					<image v-show="showIdentity" class="icon-open-eye" src="@/static/image/icon_inputopeneye.png" mode="widthFix">
					</image>
				</view>
			</view>
			<view v-for="(realName, index) in ticketInfo._realNameList" :key="index" class="ticket-item">
				<view class="ticket-realname" v-if="realName.name">
					<text>姓&emsp;名</text>
					<text>{{ realName.name }}</text>
				</view>
				<view class="ticket-realname" v-if="realName.identity">
					<text>身份证</text>
					<text v-if="!showIdentity">{{
						`${realName.identity.slice(
							0,
							3
						)}***********${realName.identity.slice(-4)}`
					}}</text>
					<text v-else-if="showIdentity">{{
						`${realName.identity}`
					}}</text>

				</view>
				<view class="ticket-img">
					<image v-if="ticketInfo.showQRCode" @click="goQRCode(ticketInfo)" class="icon"
						src="@/static/image/order/orderdetal-qrcode-icon.png" mode="aspectFill" />
					<template v-if="ticketInfo.showFace">
						<image @tap="
							Tool.goPage.push(
								`/pages/faceRecognition/faceRecognition?identity=${realName.identity}&ticketNumber=${ticketInfo.ticketNumber}&name=${realName.name}&scenicId=${ticketInfo.scenicId}`
							)
							" class="icon" src="@/static/image/order/orderdetal-face-icon.png" mode="widthFix" />
					</template>
				</view>
			</view>
		</view>
		<!-- 退订规则 -->
		<y-popup v-model="isPopUpWin" type="reserve" title="退订规则">
			<view v-if="noteContent" v-html="noteContent" class="rich-content"></view>
			<y-empty v-else>暂无内容</y-empty>
		</y-popup>
	</view>
</template>
<script setup>
import { toRefs, reactive, ref, computed, onMounted, watch } from "vue"
import { ticketType, goodsType, ticketStatus } from "@/utils/constant.js"
import ScenicAddress from "./l-scenic-address.vue"
import QRCode from "./l-QRCode.vue"
import { getEnv } from "@/utils/getEnv";

const props = defineProps({
	order: {
		type: Object,
		default: {}
	}
})

const imgHost = getEnv().VITE_IMG_HOST

const isPopUpWin = ref(false)
const noteContent = ref('')
const popModel = async () => {
	const { productSkuId } = ticketInfo.value
	const { data } = await request.get(`/appScenic/goodsNote/${productSkuId}`)
	noteContent.value = markdownToHtml(data)
	isPopUpWin.value = true
}

const selectedIndex = ref(0)
const showIdentity = ref(false)
const selectItem = (index) => {
	selectedIndex.value = index
}
const goQRCode = item => {
	console.log(item);

	Tool.goPage.push(
		`/pages/ETicket/ETicket?ticketNumber=${item.ticketNumber}&orderId=${item.orderId}`
	)
}
const switchIdentity = () => {
	showIdentity.value = !showIdentity.value;
}

const ticketInfo = computed(() => {
	const t = props.order.composeTicketList[selectedIndex.value]
	// 非实名票的二维码直接展示
	t.QRCodes = []
	if (!t.showRealName) {
		t.QRCodes = t.orderTicketInfoList.filter(e => e.printStr).map(item => {
			t.ticketNumber = item.ticketNumber
			return {
				printStr: item.printStr,
				orderId: t.orderId,
				ticketNumber: item.ticketNumber,
				ticketStatus: item.ticketStatus,

			}
		})

	}

	t._realNameList = []
	t.orderTicketInfoList.forEach(item => {
		item.realNameList.forEach(realName => {
			t._realNameList.push({
				name: realName.name,
				identity: realName.identity,
				ticketNumber: item.ticketNumber,
			})
		})
	})
	return t
})

</script>
<style lang="scss" scoped>
.ticket-info {
	.equal-width-container {
		display: flex;
		justify-content: flex-start;
		overflow-x: auto;
		gap: 20rpx;
		flex-wrap: nowrap;
		padding-bottom: 10rpx;
		margin-bottom: 30rpx;
	}

	.equal-width-item {
		flex-shrink: 0;
		min-width: 120rpx;
		flex-grow: 1;
		text-align: center;
		background: #FBFBFB;
		padding: 15rpx;
		border-radius: 8rpx;
		border: 1rpx solid #E1E1E2;
		transition: background-color 0.2s ease, border-color 0.2s ease;

		&.active {
			background: rgba(52, 159, 255, 0.09);
			border: 1px solid #349FFF;
		}
	}

	background-color: #fff;
	padding: 30rpx;
	margin: 20rpx 0;
	border-radius: 12rpx;

	.ticket-title {
		font-size: 36rpx;
		font-weight: 500;
		color: #050505;
		margin-bottom: 23rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;

		.eye {
			width: 60rpx;
			text-align: right;

			.icon-eye {
				width: 30rpx;
				height: 15rpx;
			}

			.icon-open-eye {
				width: 30rpx;
				height: 23rpx;
			}
		}
	}

	>.ticket__item {
		display: flex;

		font-size: 28rpx;
		color: #14131f;

		&:not(:last-child) {
			margin-bottom: 20rpx;
		}

		.ticket__item-title {
			display: flex;
			justify-content: space-between;
			font-size: 28rpx;
			width: 117rpx;
			color: #6e6e6e;
			margin-right: 26rpx;
		}

		.ticket__item-address {
			flex: 1;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}
	}

	.ticket-list {
		padding: 30rpx 20rpx;
		background-color: #f3f7fd;
		margin-bottom: 20rpx;
		border-radius: 12rpx;

		.ticket-list-title {
			font-size: 34rpx;
			font-weight: 500;
			padding-bottom: 30rpx;
		}

		.ticket-item {
			padding-top: 30rpx;
			border-top: 2rpx solid rgb(228, 228, 228, 0.6);

			.title {
				font-size: 30rpx;
				font-weight: 500;
				color: #050505;
			}

			.yuyue {
				margin-top: 16rpx;
				font-size: 26rpx;
				font-weight: 300;
				color: #050505;
			}

			.ticket-realname {
				display: flex;
				margin: 20rpx 0;
				font-size: 28rpx;

				text:first-child {
					color: #6e6e6e;
					width: 110rpx;
				}

				text:last-child {
					font-weight: 400;
					color: #14131f;
				}
			}

			.ticket-img {
				margin: 20rpx 0;
				display: flex;

				.icon {
					width: 80rpx;
					height: 80rpx;
					margin-right: 43rpx;
				}
			}
		}
	}
}

.ticket-no-realname {
	margin-top: 40rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}
</style>
