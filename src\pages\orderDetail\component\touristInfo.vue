<template>
	<view class="tourist-info" :theme="props.theme" v-if="props.modelValue.length > 0">
		<view class="title">
			<y-font-weight>游客信息</y-font-weight>
		</view>
		<view class="tourist-check">
			<!-- 权益卡 -->
			<template v-if="props.modelValue[0].isTravelCard">
				<view class="tourist-item" v-for="(item, index) in props.modelValue" :key="index">
					<view class="user-item">
						<div>
							<view class="input-box">
								<view class="name"> 姓&emsp;名 </view>
								<view v-if="item.name">{{ item.name }}</view>
								<view v-else>--</view>
							</view>
							<view class="input-box">
								<view class="name"> 身份证 </view>
								<view v-if="item.identity">{{
									`${item.identity.slice(0, 3)}***********${item.identity.slice(
										-4
									)}`
								}}</view>
								<view v-else>--</view>
							</view>
							<view class="input-box" v-if="props.showQRCode && item.ticketStatus">
								<view class="name"> 状&emsp;态 </view>
								<view v-if="props.showQRCode && item.ticketStatus">{{
									ticketStatus[item.ticketStatus]
								}}</view>
								<view v-else>--</view>
							</view>
						</div>
						<view class="group-picture" v-if="
							props.showQRCode &&
							item.scenicId &&
							!item.disabled &&
							item.flag == 1
						">
							<view class="item" v-if="item.ticketNumber" @tap="
								Tool.goPage.push(
									`/pages/ETicket/ETicket?ticketNumber=${item.ticketNumber}&orderId=${item.orderId}`
								)
								">
								<image class="icon" src="@/static/image/order/orderdetal-qrcode-icon.png" mode="widthFix"></image>
							</view>
							<view class="item" v-if="
								item.ticketStatus == 0 &&
								item.realNameList[0] &&
								item.realNameList[0].identity &&
								item.identityTypeList &&
								item.identityTypeList.includes('人脸') &&
								item.identityTypeList.includes('身份证')
							">
								<image @tap="
									Tool.goPage.push(
										`/pages/faceRecognition/faceRecognition?identity=${item.realNameList[0].identity}&ticketNumber=${item.ticketNumber}&name=${item.realNameList[0].name}&scenicId=${item.scenicId}`
									)
									" class="icon" src="@/static/image/order/orderdetal-face-icon.png" mode="widthFix"></image>
							</view>
						</view>
					</view>
				</view>
			</template>
			<!-- 一票一人 -->
			<template v-else-if="props.modelValue[0].issueTicketType === 0">
				<view class="tourist-item" v-for="(item, index) in props.modelValue" :key="index">
					<view class="check" v-if="props.showRefund">
						<y-checkbox :checked="item.showRefund" :disabled="item.disabled" @onCheck="onCheck(item)"></y-checkbox>
						<!-- <checkbox :checked="item.check" :disabled="item.disabled" @tap="onCheck(item)" color='#61B4FC' /> -->
					</view>
					<view class="user-item" v-if="isRefundOrder ? item.disabled : true">
						<div>
							<view class="input-box">
								<view class="name"> 姓&emsp;名 </view>
								<view v-if="item.realNameList[0]">{{
									item.realNameList[0].name
								}}</view>
								<view v-else>--</view>
							</view>
							<view class="input-box">
								<view class="name"> 身份证 </view>
								<view v-if="item.realNameList[0]">{{
									`${item.realNameList[0].identity.slice(
										0,
										3
									)}***********${item.realNameList[0].identity.slice(-4)}`
								}}</view>
								<view v-else>--</view>
							</view>
							<view class="input-box" v-if="props.showQRCode && item.ticketStatus">
								<view class="name"> 状&emsp;态 </view>
								<view v-if="props.showQRCode && item.ticketStatus">{{
									ticketStatus[item.ticketStatus]
								}}</view>
								<view v-else>--</view>
							</view>
						</div>
						<view class="group-picture" v-if="
							props.showQRCode &&
							item.scenicId &&
							!item.disabled &&
							item.flag == 1
						">
							<view class="item" v-if="item.ticketNumber" @tap="
								Tool.goPage.push(
									`/pages/ETicket/ETicket?ticketNumber=${item.ticketNumber}&orderId=${item.orderId}`
								)
								">
								<image class="icon" src="@/static/image/order/orderdetal-qrcode-icon.png" mode="widthFix"></image>
							</view>
							<view class="item" v-if="
								item.ticketStatus == 0 &&
								item.realNameList[0] &&
								item.realNameList[0].identity &&
								item.identityTypeList &&
								item.identityTypeList.includes('人脸') &&
								item.identityTypeList.includes('身份证')
							" @tap="
								Tool.goPage.push(
									`/pages/faceRecognition/faceRecognition?identity=${item.realNameList[0].identity}&ticketNumber=${item.ticketNumber}&name=${item.realNameList[0].name}&scenicId=${item.scenicId}`
								)
								">
								<image class="icon" src="@/static/image/order/orderdetal-face-icon.png" mode="widthFix">
								</image>
							</view>
						</view>
					</view>
				</view>
			</template>
			<!-- 一票多人 -->
			<template v-else>
				<view class="more-people" v-if="props.modelValue[0].flag == 1">
					<y-ticket-qrcode :qr-str="props.modelValue[0].printStr" :ticketId="props.modelValue[0].ticketNumber"
						:orderId="props.modelValue[0].orderId" :status="props.modelValue[0].ticketStatus" />
				</view>
				<view class="tourist-item" v-if="props.showQRCode && props.modelValue[0].ticketStatus">
					<view class="user-item">
						<div>
							<view class="input-box">
								<view class="name"> 状&emsp;态 </view>
								<view v-if="props.showQRCode && props.modelValue[0].ticketStatus">{{
									ticketStatus[props.modelValue[0].ticketStatus] }}</view>
								<view v-else>--</view>
							</view>
						</div>
					</view>
				</view>
				<view class="tourist-item" v-for="(item, index) in props.modelValue[0].realNameList" :key="index">
					<view class="check" v-if="props.modelValue[0].showRefund">
						<y-checkbox :checked="props.modelValue[0].showRefund" :disabled="props.modelValue[0].disabled"
							@onCheck="onCheck(item)"></y-checkbox>
					</view>
					<view class="user-item" v-if="isRefundOrder ? props.modelValue[0].disabled : true">
						<div>
							<view class="input-box">
								<view class="name"> 姓&emsp;名 </view>
								<view v-if="item.name">{{ item.name }}</view>
								<view v-else>--</view>
							</view>
							<view class="input-box">
								<view class="name"> 身份证 </view>
								<view v-if="item.identity">{{
									`${item.identity.slice(0, 3)}***********${item.identity.slice(
										-4
									)}`
								}}</view>
								<view v-else>--</view>
							</view>
						</div>

						<view class="group-picture" v-if="
							props.showQRCode &&
							props.modelValue[0].scenicId &&
							!props.modelValue[0].disabled
						">
							<view class="item" v-if="
								props.modelValue[0].ticketStatus == 0 &&
								item.identity &&
								props.modelValue[0].identityTypeList &&
								props.modelValue[0].identityTypeList.includes('人脸') &&
								props.modelValue[0].identityTypeList.includes('身份证')
							">
								<image @tap="
									Tool.goPage.push(
										`/pages/faceRecognition/faceRecognition?identity=${item.identity}&ticketNumber=${props.modelValue[0].ticketNumber}&name=${item.name}&scenicId=${props.modelValue[0].scenicId}`
									)
									" class="icon" src="@/static/image/order/orderdetal-face-icon.png" mode="widthFix"></image>
							</view>
						</view>
					</view>
				</view>
			</template>
		</view>
	</view>
</template>
<script setup>
import { toRefs, reactive, ref, watch, onMounted } from "vue"
import { ticketStatus } from "@/utils/constant.js"
import request from "@/utils/request.js"
const props = defineProps({
	modelValue: {
		type: Array,
		default: []
	},
	//主题
	theme: {
		type: String,
		default: "redTheme"
	},
	//退票勾选展示
	showRefund: {
		type: Boolean,
		default: false
	},
	//二维码显示
	showQRCode: {
		type: Boolean,
		default: false
	},
	//是否退单
	isRefundOrder: {
		type: Boolean,
		default: false
	}
})
console.log(props)
console.log("props==========")
const aaa = ref(false)
const qrCodeWidth = uni.upx2px(466)


watch(
	() => props,
	value => {
		console.log("touristInfo", value)
	},
	{ deep: true }
)
watch(
	() => props.showQRCode,
	async value => {
		let bool = true
		for (let i = 0; i < props.modelValue.length; i++) {
			const item = props.modelValue[i]
			if (item.checkId && props.showQRCode) {
				//查询每张票的检票规则
				console.log("查询每张票的检票规则")
				const identityTypeList = await getCheckRule(item.checkId)
				if (identityTypeList) {
					//放到源数据上
					item.identityTypeList = identityTypeList
					if (identityTypeList.includes("身份证") && bool) {
						emits("isSFZ", true)
						bool = false
					}
				}
			}
		}
	}
)
const emits = defineEmits(["isSFZ"]) //提示父页面是否需要身份证
onMounted(async () => { })
//获取检票规则
const getCheckRule = async id => {
	try {
		// uni.showLoading({mask:true});
		const { code, data } = await request.get(`/ticketCheck/info`, { id: id })
		return data.identityTypeList
	} catch (err) {
		console.log(err)
	}
	return null
	// uni.hideLoading();
}
const onCheck = item => {
	item.showRefund = !item.showRefund
}
</script>
<style lang="scss" scoped>
.tourist-info[theme="redTheme"] {
	--color1: #d43e3e;
	--color2: #ffb4b4;
	--bg1: #fff8f8;
}

.tourist-info[theme="blueTheme"] {
	--color1: #024abb;
	--color2: #61b4fc;
	--bg1: #f8fbff;
}

.image {
	margin-top: 30rpx;
	width: 100rpx !important;
}

.icon {
	height: 100%;
	width: 100%;
}

.tourist-info {
	background-color: #fff;
	border-radius: 18rpx;
	// margin-bottom: 30rpx;
	margin-top: 30rpx;
	padding: 34rpx 30rpx 30rpx;

	>.title {
		display: flex;
		align-items: center;
		padding-bottom: 30rpx;
		font-size: 34rpx;
		font-weight: 500;
		color: #050505;
		// background-color: var(--color2);
		border-radius: 24px 24px 0px 0px;
		border-bottom: 2rpx solid rgba(228, 228, 228, 0.39);
	}

	.tourist-check {
		width: 100%;
		display: flex;
		flex-direction: column;

		// flex-direction: column;
		.more-people {
			display: flex;
			flex-direction: column;
			align-items: center;

			.icon {
				width: 80%;
			}

			.ticket-num {
				margin-top: 10px;
			}
		}

		.tourist-item {
			display: flex;
			width: 100%;
			background-color: var(--bg1);
			overflow: hidden;

			&:not(:last-child) {
				border-bottom: 2rpx solid rgba(228, 228, 228, 0.39);
			}

			.user-item {
				display: flex;
				justify-content: space-between;
				flex: 1;
				margin: 33rpx 0;

				.input-box {
					display: flex;
					align-items: center;
					margin-bottom: 30rpx;
					font-size: 28rpx;
					font-weight: 400;
					color: #040404;

					&:last-child {
						margin-bottom: 0;
					}

					.name {
						margin-right: 37rpx;
						font-family: none;
					}
				}
			}

			.group-picture {
				display: flex;
				margin-top: 41rpx;
				margin-bottom: 20rpx;

				.item {
					height: 80rpx;
					width: 80rpx;
					margin-right: 42rpx;
				}
			}

			.check {
				margin-right: 23rpx;
				display: flex;
				justify-content: center;
				align-items: center;
			}
		}
	}
}

.add-ticket {
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 20rpx;
	height: 96rpx;
	font-size: 36rpx;
	font-weight: 500;
	color: #ffffff;
	background: #7ed3da;
	border-radius: 25rpx;
}
</style>
